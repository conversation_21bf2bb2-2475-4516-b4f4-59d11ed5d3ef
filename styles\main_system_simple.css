/*
ملف الأنماط المبسط للنظام التعليمي الشامل
بدون تموجات أو تأثيرات متحركة - أداء محسن
*/

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    /* إزالة جميع التأثيرات المتحركة */
    animation: none !important;
    transition: none !important;
}

body {
    font-family: '<PERSON><PERSON>ri', 'Tahoma', 'Arial', sans-serif;
    font-size: 14px;
    font-weight: bold;
    background: #667eea;
    min-height: 100vh;
    padding: 20px;
    direction: rtl;
}

.main-container {
    max-width: 1200px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.header {
    background: #9b59b6;
    color: white;
    padding: 40px;
    text-align: center;
}

.header h1 {
    font-size: 36px;
    margin-bottom: 15px;
}

.header p {
    font-size: 18px;
    opacity: 0.95;
}

.status-bar {
    background: #f8f9fa;
    padding: 15px 30px;
    font-size: 13px;
    color: #495057;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dee2e6;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #28a745;
}

.dashboard-content {
    padding: 40px;
}

.quick-access {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid #dee2e6;
}

.quick-access h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.quick-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.quick-link {
    background: white;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    padding: 12px 20px;
    text-decoration: none;
    color: #495057;
    font-size: 13px;
    font-weight: bold;
    text-align: center;
    cursor: pointer;
}

.quick-link:hover {
    background: #f8f9fa;
    border-color: #9b59b6;
    color: #9b59b6;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin: 30px 0;
}

.stat-item {
    background: #667eea;
    color: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.stat-item:hover {
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.stat-value {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 600;
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.module-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.module-card:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border-color: #9b59b6;
}

.module-icon {
    font-size: 60px;
    margin-bottom: 20px;
    display: block;
}

.module-title {
    font-size: 22px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 15px;
}

.module-description {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 25px;
    line-height: 1.6;
}

.module-button {
    background: #9b59b6;
    color: white;
    border: none;
    border-radius: 12px;
    padding: 15px 30px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.module-button:hover {
    background: #8e44ad;
}

.module-button.secondary {
    background: #3498db;
}

.module-button.secondary:hover {
    background: #2980b9;
}

.module-button.success {
    background: #2ecc71;
}

.module-button.success:hover {
    background: #27ae60;
}

.module-button.warning {
    background: #f39c12;
}

.module-button.warning:hover {
    background: #e67e22;
}

.footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 30px;
    font-size: 13px;
}

.footer p {
    margin-bottom: 5px;
    opacity: 0.9;
}

/* تجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .quick-links {
        grid-template-columns: 1fr;
    }
    
    .modules-grid {
        grid-template-columns: 1fr;
    }
    
    .header h1 {
        font-size: 28px;
    }
    
    .dashboard-content {
        padding: 20px;
    }
    
    body {
        padding: 10px;
    }
}

/* تحسينات الأداء */
.main-container {
    position: relative;
}

/* ألوان إضافية بسيطة */
.color-blue { background: #3498db; }
.color-green { background: #2ecc71; }
.color-orange { background: #f39c12; }
.color-red { background: #e74c3c; }
.color-purple { background: #9b59b6; }
.color-teal { background: #1abc9c; }
