"""
نظام استيراد البيانات الحديث - تحويل sub1_window.py إلى منهجية Python + HTML
يجمع بين قوة Python في معالجة البيانات مع واجهة HTML تفاعلية عبر QWebEngineView

تحويل من sub1_window.py إلى نظام بايثون + HTML بدون خادم
"""

import sys
import os
import json
import sqlite3
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QFileDialog, QMessageBox
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, QUrl, QDateTime, pyqtSignal, Qt
from PyQt5.QtGui import QIcon

# محاولة استيراد pandas مع معالجة الخطأ
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

class DataImportEngine(QObject):
    """محرك استيراد البيانات - مسؤول عن كل المعالجة والتفاعل مع قاعدة البيانات"""
    
    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    importProgressUpdated = pyqtSignal(int, str)  # progress percentage, status text
    statisticsUpdated = pyqtSignal(str)  # statistics JSON
    
    def __init__(self):
        super().__init__()
        self.db_path = "data.db"
        self.pandas_available = PANDAS_AVAILABLE
    
    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = QDateTime.currentDateTime().toString("hh:mm:ss")
        self.logUpdated.emit(message, status, timestamp)
    
    @pyqtSlot(result=bool)
    def checkPandasAvailability(self):
        """التحقق من توفر pandas"""
        return self.pandas_available
    
    @pyqtSlot(result=str)
    def getDatabasePath(self):
        """الحصول على مسار قاعدة البيانات"""
        return self.db_path
    
    @pyqtSlot(result=str)
    def getDatabaseStatistics(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جمع الإحصائيات
            stats = {}
            
            # السنة الدراسية الحالية
            try:
                cursor.execute("SELECT DISTINCT السنة_الدراسية FROM اللوائح ORDER BY السنة_الدراسية DESC LIMIT 1")
                result = cursor.fetchone()
                stats['current_year'] = result[0] if result else "غير محدد"
            except:
                stats['current_year'] = "غير محدد"
            
            # إحصائيات الأقسام والتلاميذ
            try:
                cursor.execute("""
                    SELECT 
                        COUNT(DISTINCT القسم) as sections_count,
                        COUNT(*) as students_count,
                        COUNT(DISTINCT المستوى) as levels_count
                    FROM اللوائح 
                    WHERE السنة_الدراسية = ?
                """, (stats['current_year'],))
                
                db_stats = cursor.fetchone()
                if db_stats:
                    stats['sections_count'] = db_stats[0]
                    stats['students_count'] = db_stats[1] 
                    stats['levels_count'] = db_stats[2]
                else:
                    stats['sections_count'] = 0
                    stats['students_count'] = 0
                    stats['levels_count'] = 0
            except:
                stats['sections_count'] = 0
                stats['students_count'] = 0
                stats['levels_count'] = 0
            
            # قائمة الأقسام
            try:
                cursor.execute("SELECT DISTINCT القسم FROM اللوائح WHERE السنة_الدراسية = ? ORDER BY القسم", (stats['current_year'],))
                sections = [row[0] for row in cursor.fetchall()]
                stats['sections'] = sections
            except:
                stats['sections'] = []
            
            # إحصائيات الأساتذة
            try:
                cursor.execute("SELECT COUNT(*) FROM الأساتذة")
                result = cursor.fetchone()
                stats['teachers_count'] = result[0] if result else 0
            except:
                stats['teachers_count'] = 0
            
            # إحصائيات الرموز السرية
            try:
                cursor.execute("SELECT COUNT(*) FROM الرمز_السري")
                result = cursor.fetchone()
                stats['secret_codes_count'] = result[0] if result else 0
            except:
                stats['secret_codes_count'] = 0
            
            conn.close()
            return json.dumps(stats, ensure_ascii=False)
            
        except Exception as e:
            self.emit_log(f"خطأ في جمع الإحصائيات: {str(e)}", "error")
            return json.dumps({"error": str(e)}, ensure_ascii=False)
    
    @pyqtSlot()
    def selectMasarFile(self):
        """اختيار ملف لوائح منظومة مسار"""
        if not self.pandas_available:
            self.emit_log("خطأ: مكتبة pandas غير متوفرة", "error")
            self.emit_log("لتثبيت pandas، استخدم الأمر: pip install pandas", "info")
            return
        
        # استخدام QFileDialog لاختيار الملف
        file_path, _ = QFileDialog.getOpenFileName(
            None, "اختر ملف الإكسل", "", "Excel Files (*.xlsx *.xls)"
        )
        
        if file_path:
            self.importMasarData(file_path)
    
    def importMasarData(self, file_path):
        """استيراد بيانات منظومة مسار"""
        self.emit_log("بدء استيراد بيانات منظومة مسار...", "progress")
        self.importProgressUpdated.emit(0, "جاري تحضير الملف...")
        
        conn = None
        try:
            # التحقق من اسم الملف
            file_name = os.path.basename(file_path)
            if "ListEleve" not in file_name:
                self.emit_log(f"تحذير: الملف {file_name} قد لا يكون ملف لوائح منظومة مسار", "warning")
            
            # إنشاء اتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # حذف السجلات الافتراضية
            self.importProgressUpdated.emit(10, "جاري حذف السجلات الافتراضية...")
            cursor.execute("DELETE FROM السجل_العام WHERE الرمز = 'A12345678'")
            cursor.execute("DELETE FROM اللوائح WHERE الرمز = 'A12345678'")
            cursor.execute("DROP TABLE IF EXISTS 'السجل الاولي'")
            
            # قراءة ملف Excel
            self.importProgressUpdated.emit(20, "جاري قراءة ملف Excel...")
            sheets_dict = pd.read_excel(file_path, sheet_name=None)
            
            # استخراج السنة الدراسية
            current_academic_year = None
            for sheet_name, df in sheets_dict.items():
                if "Unnamed: 6" in df.columns and len(df) > 5:
                    current_academic_year = df.iloc[5]["Unnamed: 6"]
                    break
            
            # تحضير البيانات
            self.importProgressUpdated.emit(30, "جاري تحضير البيانات...")
            total_sheets = len(sheets_dict)
            
            for i, (sheet_name, df) in enumerate(sheets_dict.items()):
                progress = 30 + int((i / total_sheets) * 20)
                self.importProgressUpdated.emit(progress, f"جاري معالجة القسم {sheet_name}...")
                
                level_rows = df[df["Unnamed: 0"].astype(str).str.strip() == ": المستوى"]
                level_value = level_rows.iloc[0]["Unnamed: 2"] if not level_rows.empty else None
                year_value = df.iloc[5]["Unnamed: 6"] if "Unnamed: 6" in df.columns and len(df) > 5 else None
                
                df["القسم"] = sheet_name
                df["المستوى"] = level_value
                df["السنة الدراسية"] = year_value
            
            # دمج وحفظ البيانات
            self.importProgressUpdated.emit(50, "جاري دمج وحفظ البيانات...")
            combined_df = pd.concat(sheets_dict.values(), ignore_index=True)
            combined_df.to_sql("السجل الاولي", conn, if_exists='append', index=False)
            
            # معالجة البيانات
            self.importProgressUpdated.emit(60, "جاري معالجة البيانات...")
            self.process_data_silently(cursor, current_academic_year)
            
            # حفظ التغييرات
            self.importProgressUpdated.emit(90, "جاري حفظ التغييرات...")
            conn.commit()
            conn.close()
            conn = None
            
            # عرض النتائج
            self.importProgressUpdated.emit(100, "تم الانتهاء بنجاح!")
            self.emit_log(f"✅ تم استيراد بيانات منظومة مسار بنجاح للسنة الدراسية {current_academic_year}", "success")
            
            # تحديث الإحصائيات
            self.statisticsUpdated.emit(self.getDatabaseStatistics())
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في استيراد البيانات: {str(e)}", "error")
            if conn:
                conn.close()
    
    def process_data_silently(self, cursor, academic_year):
        """معالجة البيانات بهدوء"""
        try:
            # تحديث بيانات المؤسسة
            self.update_school_info(cursor)
            
            # إنشاء وتحديث اللوائح
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS 'اللوائح' (
                    'السنة_الدراسية' TEXT, 
                    'القسم' TEXT, 
                    'المستوى' TEXT, 
                    'الرمز' TEXT, 
                    'رت' TEXT, 
                    'مجموع التلاميذ' INTEGER DEFAULT 0, 
                    PRIMARY KEY('السنة_الدراسية', 'الرمز')
                )
            """)
            
            # حذف البيانات القديمة للسنة الدراسية
            if academic_year:
                cursor.execute("DELETE FROM اللوائح WHERE السنة_الدراسية = ?", (academic_year,))
            
            # إضافة البيانات الجديدة
            cursor.execute("""
                INSERT OR IGNORE INTO "اللوائح" ("السنة_الدراسية", "القسم", "المستوى", "الرمز", "رت")
                SELECT "السنة الدراسية", "القسم", "المستوى", "Unnamed: 1", "Unnamed: 0"
                FROM "السجل الاولي"
            """)
            
            # تنظيف البيانات
            cursor.execute("""
                DELETE FROM "اللوائح" 
                WHERE "الرمز" IS NULL OR TRIM("الرمز") = '' OR "الرمز" = 'الرمز'
            """)
            
            # تحديث مجموع التلاميذ
            if academic_year:
                cursor.execute("""
                    UPDATE "اللوائح" as l1
                    SET "مجموع التلاميذ" = (
                        SELECT COUNT(*)
                        FROM "اللوائح" AS l2
                        WHERE l2."القسم" = l1."القسم"
                        AND l2."السنة_الدراسية" = l1."السنة_الدراسية"
                    )
                    WHERE l1."السنة_الدراسية" = ?
                """, (academic_year,))
            
            # تحديث السجل العام والبنية التربوية
            self.update_structure_tables(cursor, academic_year)
            
        except Exception as e:
            raise Exception(f"حدث خطأ أثناء معالجة البيانات: {str(e)}")
    
    def update_school_info(self, cursor):
        """تحديث بيانات المؤسسة"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS بيانات_المؤسسة (
                الأكاديمية TEXT,
                المديرية TEXT,
                الجماعة TEXT,
                المؤسسة TEXT,
                السنة_الدراسية TEXT,
                البلدة TEXT,
                المدير TEXT,
                الحارس_العام TEXT,
                السلك TEXT,
                رقم_الحراسة TEXT,
                رقم_التسجيل TEXT,
                الأسدس TEXT,
                ImagePath1 TEXT
            )
        """)
        
        # استخراج بيانات المؤسسة من السجل الأولي
        cursor.execute("SELECT * FROM 'السجل الاولي' LIMIT 6")
        rows = cursor.fetchall()
        
        if len(rows) >= 6:
            # إضافة أو تحديث بيانات المؤسسة
            cursor.execute("DELETE FROM بيانات_المؤسسة")
            cursor.execute("""
                INSERT INTO بيانات_المؤسسة 
                (الأكاديمية, المديرية, الجماعة, المؤسسة, السنة_الدراسية, الأسدس)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                rows[3][2] if len(rows) > 3 else '',  # الأكاديمية
                rows[4][2] if len(rows) > 4 else '',  # المديرية  
                rows[3][6] if len(rows) > 3 else '',  # الجماعة
                rows[4][6] if len(rows) > 4 else '',  # المؤسسة
                rows[5][6] if len(rows) > 5 else '',  # السنة الدراسية
                'الأول'  # الأسدس
            ))
    
    def update_structure_tables(self, cursor, academic_year):
        """تحديث جداول البنية التربوية والسجل العام"""
        # تحديث السجل العام
        cursor.execute("""
            INSERT OR IGNORE INTO "السجل_العام"
                ("الرمز", "الاسم_والنسب", "النوع", "تاريخ_الازدياد", "مكان_الازدياد")
            SELECT "Unnamed: 1",
                   "Unnamed: 2" || ' ' || "Unnamed: 3",
                   "Unnamed: 4",
                   "Unnamed: 5", 
                   "Unnamed: 6"
            FROM "السجل الاولي"
            WHERE "Unnamed: 1" IS NOT NULL
              AND TRIM("Unnamed: 1") <> ''
              AND "Unnamed: 1" <> 'الرمز'
        """)
        
        # تحديث البنية التربوية
        cursor.execute("""
            INSERT OR IGNORE INTO "البنية_التربوية" ("السنة_الدراسية", "القسم", "المستوى")
            SELECT DISTINCT "السنة_الدراسية", "القسم", "المستوى"
            FROM "اللوائح"
        """)
        
        # إضافة عمود مجموع التلاميذ إذا لم يكن موجوداً
        try:
            cursor.execute('ALTER TABLE "البنية_التربوية" ADD COLUMN "مجموع_التلاميذ" INTEGER DEFAULT 0')
        except sqlite3.OperationalError:
            pass
        
        # تحديث مجموع التلاميذ في البنية التربوية
        if academic_year:
            cursor.execute("""
                UPDATE "البنية_التربوية"
                SET "مجموع_التلاميذ" = (
                    SELECT COUNT(*)
                    FROM "اللوائح" AS l
                    WHERE l."السنة_الدراسية" = "البنية_التربوية"."السنة_الدراسية"
                      AND l."القسم" = "البنية_التربوية"."القسم"
                      AND l."المستوى" = "البنية_التربوية"."المستوى"
                )
                WHERE "السنة_الدراسية" = ?
            """, (academic_year,))

    @pyqtSlot()
    def selectTeachersFile(self):
        """اختيار ملف أسماء الأساتذة"""
        if not self.pandas_available:
            self.emit_log("خطأ: مكتبة pandas غير متوفرة", "error")
            return

        file_path, _ = QFileDialog.getOpenFileName(
            None, "اختر ملف الأساتذة", "", "Excel Files (*.xlsx *.xls)"
        )

        if file_path:
            self.importTeachersData(file_path)

    def importTeachersData(self, file_path):
        """استيراد أسماء الأساتذة والمواد"""
        self.emit_log("بدء استيراد أسماء الأساتذة...", "progress")
        self.importProgressUpdated.emit(0, "جاري تحضير البيانات...")

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء جدول الأساتذة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS الأساتذة (
                    رقم_الأستاذ INTEGER PRIMARY KEY AUTOINCREMENT,
                    اسم_الأستاذ TEXT,
                    المادة TEXT,
                    الرمز TEXT
                )
            ''')

            # حذف البيانات السابقة
            self.importProgressUpdated.emit(20, "جاري حذف البيانات السابقة...")
            cursor.execute("DELETE FROM الأساتذة")

            # قراءة ملف Excel
            self.importProgressUpdated.emit(40, "جاري قراءة ملف Excel...")
            df = pd.read_excel(file_path, header=None)

            if df.empty:
                self.emit_log("الملف فارغ", "warning")
                return

            # معالجة البيانات
            self.importProgressUpdated.emit(60, "جاري معالجة البيانات...")
            df_selected = df[[1, 2, 3]].copy()  # B, C, D columns
            df_selected.columns = ['المادة', 'الرمز', 'اسم_الأستاذ']

            # ملء الفراغات في عمود المادة
            previous_subject = None
            for index, row in df_selected.iterrows():
                current_subject = row['المادة']
                if pd.isna(current_subject) or (isinstance(current_subject, str) and current_subject.strip() == ''):
                    if previous_subject is not None:
                        df_selected.at[index, 'المادة'] = previous_subject
                else:
                    previous_subject = current_subject

            # تنظيف البيانات
            df_selected = df_selected.dropna(subset=['اسم_الأستاذ'])
            df_selected = df_selected[~df_selected['اسم_الأستاذ'].str.contains('المجموع الاجمالي', na=False)]

            # إدراج البيانات
            self.importProgressUpdated.emit(80, "جاري إدراج البيانات...")
            records_inserted = 0

            for _, row in df_selected.iterrows():
                if row['اسم_الأستاذ'].strip() and row['المادة'].strip():
                    cursor.execute(
                        "INSERT INTO الأساتذة (اسم_الأستاذ, المادة, الرمز) VALUES (?, ?, ?)",
                        (row['اسم_الأستاذ'], row['المادة'], row['الرمز'])
                    )
                    records_inserted += 1

            conn.commit()
            conn.close()
            conn = None

            self.importProgressUpdated.emit(100, "تم الانتهاء!")
            self.emit_log(f"✅ تم استيراد {records_inserted} أستاذ بنجاح", "success")
            self.statisticsUpdated.emit(self.getDatabaseStatistics())

        except Exception as e:
            self.emit_log(f"❌ خطأ في استيراد الأساتذة: {str(e)}", "error")
            if conn:
                conn.close()

    @pyqtSlot()
    def selectSecretCodesFiles(self):
        """اختيار ملفات الرموز السرية"""
        if not self.pandas_available:
            self.emit_log("خطأ: مكتبة pandas غير متوفرة", "error")
            return

        file_paths, _ = QFileDialog.getOpenFileNames(
            None, "اختر ملفات الرموز السرية", "", "Excel Files (*.xlsx *.xls)"
        )

        if file_paths:
            self.importSecretCodes(file_paths)

    def importSecretCodes(self, file_paths):
        """استيراد الرموز السرية من ملفات متعددة"""
        self.emit_log(f"بدء استيراد الرموز السرية من {len(file_paths)} ملف...", "progress")
        self.importProgressUpdated.emit(0, "جاري تحضير قاعدة البيانات...")

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء جدول الرمز السري
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS الرمز_السري (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    الرمز TEXT UNIQUE,
                    الرمز_السري TEXT
                )
            ''')

            # حذف البيانات السابقة
            cursor.execute("DELETE FROM الرمز_السري")

            total_records_imported = 0

            # معالجة كل ملف
            for file_index, file_path in enumerate(file_paths):
                file_name = os.path.basename(file_path)
                progress = int((file_index / len(file_paths)) * 80)
                self.importProgressUpdated.emit(progress, f"جاري معالجة الملف {file_index + 1}/{len(file_paths)}: {file_name}")

                try:
                    df = pd.read_excel(file_path)
                    if df.empty:
                        continue

                    # تحديد الأعمدة المطلوبة (افتراض أول عمودين)
                    if df.shape[1] >= 2:
                        records_data = []
                        for _, row in df.iterrows():
                            code = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else ""
                            secret = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else ""

                            if code and secret and len(code) > 2 and len(secret) > 2:
                                records_data.append((code, secret))

                        # إدراج البيانات
                        for code, secret in records_data:
                            try:
                                cursor.execute("INSERT OR REPLACE INTO الرمز_السري (الرمز, الرمز_السري) VALUES (?, ?)", (code, secret))
                                total_records_imported += 1
                            except Exception as e:
                                self.emit_log(f"خطأ في إدراج الرمز {code}: {str(e)}", "warning")

                except Exception as e:
                    self.emit_log(f"خطأ في معالجة الملف {file_name}: {str(e)}", "error")

            conn.commit()
            conn.close()
            conn = None

            self.importProgressUpdated.emit(100, "تم الانتهاء!")
            self.emit_log(f"✅ تم استيراد {total_records_imported} رمز سري بنجاح", "success")
            self.statisticsUpdated.emit(self.getDatabaseStatistics())

        except Exception as e:
            self.emit_log(f"❌ خطأ في استيراد الرموز السرية: {str(e)}", "error")
            if conn:
                conn.close()

    @pyqtSlot()
    def importArrivalsData(self):
        """استيراد بيانات الوافدين والمغادرين"""
        if not self.pandas_available:
            self.emit_log("خطأ: مكتبة pandas غير متوفرة", "error")
            return

        file_path, _ = QFileDialog.getOpenFileName(
            None, "اختر ملف الوافدين والمغادرين", "", "Excel Files (*.xlsx *.xls)"
        )

        if file_path:
            self.processArrivalsData(file_path)

    def processArrivalsData(self, file_path):
        """معالجة بيانات الوافدين والمغادرين"""
        self.emit_log("بدء استيراد بيانات الوافدين والمغادرين...", "progress")
        self.importProgressUpdated.emit(0, "جاري قراءة الملف...")

        try:
            # قراءة الملف
            df = pd.read_excel(file_path)

            if df.empty:
                self.emit_log("الملف فارغ", "warning")
                return

            self.importProgressUpdated.emit(50, "جاري معالجة البيانات...")

            # معالجة البيانات حسب الحاجة
            # يمكن تخصيص هذا الجزء حسب تنسيق ملف الوافدين والمغادرين

            self.importProgressUpdated.emit(100, "تم الانتهاء!")
            self.emit_log(f"✅ تم استيراد بيانات الوافدين والمغادرين بنجاح", "success")

        except Exception as e:
            self.emit_log(f"❌ خطأ في استيراد بيانات الوافدين والمغادرين: {str(e)}", "error")

    @pyqtSlot()
    def refreshData(self):
        """تحديث البيانات والإحصائيات"""
        self.emit_log("🔄 جاري تحديث البيانات...", "progress")
        try:
            # تحديث الإحصائيات
            self.statisticsUpdated.emit(self.getDatabaseStatistics())
            self.emit_log("✅ تم تحديث البيانات بنجاح", "success")
        except Exception as e:
            self.emit_log(f"❌ خطأ في تحديث البيانات: {str(e)}", "error")

    @pyqtSlot()
    def showHelpGuide(self):
        """عرض دليل المساعدة"""
        self.emit_log("📖 عرض دليل المساعدة...", "info")
        help_text = """
        🔹 استيراد اللوائح من منظومة مسار: لاستيراد بيانات الطلاب من ملفات Excel
        🔹 استيراد الرمز السري: لاستيراد الرموز السرية للطلاب
        🔹 استيراد أسماء الأساتذة: لاستيراد بيانات الأساتذة والمواد
        🔹 استيراد الوافدين والمغادرين: لاستيراد بيانات الطلاب الوافدين والمغادرين
        """
        self.emit_log(help_text, "info")

    @pyqtSlot()
    def returnToMainWindow(self):
        """العودة للنافذة الرئيسية"""
        self.emit_log("🔙 العودة للنافذة الرئيسية...", "info")
        self.returnToMain.emit()

class ModernSub1Window(QMainWindow):
    """النظام الحديث لاستيراد البيانات - يجمع بين Python و HTML"""

    # إشارة للعودة للنافذة الرئيسية
    returnToMain = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.setWindowTitle("🚀 نظام استيراد البيانات الحديث")
        self.setFixedSize(1000, 600)

        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك استيراد البيانات
        self.data_engine = DataImportEngine()

        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء عرض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)

        # تحميل واجهة HTML
        html_content = self.get_html_interface()
        self.web_view.setHtml(html_content)

    def setup_web_channel(self):
        """إعداد قناة التواصل مع JavaScript"""
        self.channel = QWebChannel()
        self.channel.registerObject("dataEngine", self.data_engine)
        self.channel.registerObject("importWindow", self)
        self.web_view.page().setWebChannel(self.channel)

        # انتظار تحميل الصفحة قبل إعداد القناة
        self.web_view.loadFinished.connect(self.on_page_loaded)

    def on_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة"""
        # إعادة تسجيل الكائن للتأكد من الربط
        self.channel.registerObject("dataEngine", self.data_engine)
        self.channel.registerObject("importWindow", self)

    def get_html_interface(self):
        """إنشاء واجهة HTML التفاعلية"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام استيراد البيانات الحديث</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Calibri', 'Tahoma', 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 95%;
            margin: 20px auto;
            display: grid;
            grid-template-rows: auto auto 1fr;
            gap: 20px;
            height: calc(100vh - 40px);
        }

        /* شريط العنوان */
        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #667eea;
            font-size: 24px;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 14px;
        }

        /* أزرار الاستيراد */
        .import-buttons {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .buttons-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .import-button {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 15px;
            font-size: 13px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .import-button:hover {
            background: linear-gradient(135deg, #8e44ad, #7d3c98);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(155, 89, 182, 0.4);
        }

        .import-button:active {
            transform: translateY(0);
        }

        /* منطقة السجلات */
        .log-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }

        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .log-title {
            color: #667eea;
            font-size: 18px;
            font-weight: bold;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .action-button {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 15px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-button:hover {
            background: linear-gradient(135deg, #27ae60, #229954);
            transform: translateY(-1px);
        }

        .help-button {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .help-button:hover {
            background: linear-gradient(135deg, #2980b9, #21618c);
        }

        .log-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.6;
            flex: 1;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 5px 10px;
            border-radius: 5px;
            display: flex;
            align-items: center;
        }

        .log-entry.info {
            background: rgba(52, 152, 219, 0.1);
            border-left: 4px solid #3498db;
        }

        .log-entry.success {
            background: rgba(46, 204, 113, 0.1);
            border-left: 4px solid #2ecc71;
        }

        .log-entry.error {
            background: rgba(231, 76, 60, 0.1);
            border-left: 4px solid #e74c3c;
        }

        .log-entry.warning {
            background: rgba(243, 156, 18, 0.1);
            border-left: 4px solid #f39c12;
        }

        .log-entry.progress {
            background: rgba(155, 89, 182, 0.1);
            border-left: 4px solid #9b59b6;
        }

        .log-timestamp {
            color: #7f8c8d;
            margin-left: auto;
            font-size: 11px;
        }

        /* شريط التقدم */
        .progress-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            min-width: 400px;
            z-index: 1000;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 25px;
            background: #ecf0f1;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.4s ease;
            border-radius: 12px;
        }

        .progress-text {
            text-align: center;
            color: #2c3e50;
            font-weight: bold;
            font-size: 14px;
        }

        /* إحصائيات سريعة */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 15px;
        }

        .stat-item {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            font-size: 12px;
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 10px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- شريط العنوان -->
        <div class="header">
            <h1>🚀 نظام استيراد البيانات الحديث</h1>
            <p>يمكنك استخدام الأزرار أدناه للوصول إلى وظائف الاستيراد المختلفة</p>
        </div>

        <!-- أزرار الاستيراد -->
        <div class="import-buttons">
            <div class="buttons-grid">
                <button class="import-button" onclick="importMasarData()">
                    📚 استيراد اللوائح من منظومة مسار باللغة العربية
                </button>
                <button class="import-button" onclick="importSecretCodes()">
                    🔐 استيراد الرمز السري وتحيينه دفعة واحدة
                </button>
                <button class="import-button" onclick="importTeachers()">
                    👨‍🏫 استيراد أسماء الأساتذة والمواد المدرسة
                </button>
                <button class="import-button" onclick="importArrivals()">
                    🔄 استيراد بيانات الوافدين والمغادرين
                </button>
            </div>
        </div>

        <!-- منطقة السجلات -->
        <div class="log-panel">
            <div class="log-header">
                <div class="log-title">📋 سجل العمليات</div>
                <div class="action-buttons">
                    <button class="action-button" onclick="returnToMain()" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
                        🔙 العودة للرئيسية
                    </button>
                    <button class="action-button" onclick="refreshData()">
                        🔄 تحديث البيانات
                    </button>
                    <button class="action-button help-button" onclick="showHelp()">
                        ❓ تعليمات الاستخدام
                    </button>
                </div>
            </div>

            <div class="log-container" id="logContainer">
                <!-- سيتم إضافة السجلات هنا ديناميكياً -->
            </div>

            <!-- إحصائيات سريعة -->
            <div class="stats-container" id="statsContainer">
                <div class="stat-item">
                    <div class="stat-value" id="studentsCount">0</div>
                    <div class="stat-label">الطلاب</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="teachersCount">0</div>
                    <div class="stat-label">الأساتذة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="sectionsCount">0</div>
                    <div class="stat-label">الأقسام</div>
                </div>
            </div>
        </div>
    </div>

    <!-- شريط التقدم -->
    <div class="progress-container" id="progressContainer">
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        <div class="progress-text" id="progressText">جاري المعالجة...</div>
    </div>

    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <script>
        let dataEngine = null;
        let isChannelReady = false;

        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    dataEngine = channel.objects.dataEngine;
                    isChannelReady = true;

                    console.log('🚀 QWebChannel initialized successfully');

                    // ربط الإشارات
                    if (dataEngine) {
                        dataEngine.logUpdated.connect(addLogEntry);
                        dataEngine.importProgressUpdated.connect(updateProgress);
                        dataEngine.statisticsUpdated.connect(updateStatistics);

                        // تحميل الإحصائيات عند البدء
                        loadStatistics();
                        checkPandasAvailability();

                        // رسالة ترحيبية
                        addLogEntry('أهلاً بك في نظام استيراد البيانات الحديث', 'info', new Date().toLocaleTimeString('ar-EG'));
                        addLogEntry('يمكنك استخدام الأزرار في الأعلى للوصول إلى وظائف الاستيراد المختلفة', 'info', new Date().toLocaleTimeString('ar-EG'));
                    }
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // إضافة إدخال إلى السجل
        function addLogEntry(message, status, timestamp) {
            const logContainer = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry ${status}`;

            const statusIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️',
                'progress': '🔄'
            }[status] || 'ℹ️';

            entry.innerHTML = `
                <span>${statusIcon} ${message}</span>
                <span class="log-timestamp">${timestamp}</span>
            `;

            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // تحديث شريط التقدم
        function updateProgress(percentage, statusText) {
            const progressContainer = document.getElementById('progressContainer');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            if (percentage > 0 && percentage < 100) {
                progressContainer.style.display = 'block';
                progressFill.style.width = percentage + '%';
                progressText.textContent = statusText;
            } else if (percentage >= 100) {
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                }, 1000);
            }
        }

        // تحديث الإحصائيات
        function updateStatistics(statsJson) {
            try {
                const stats = JSON.parse(statsJson);

                document.getElementById('studentsCount').textContent = stats.students_count || 0;
                document.getElementById('teachersCount').textContent = stats.teachers_count || 0;
                document.getElementById('sectionsCount').textContent = stats.sections_count || 0;

            } catch (error) {
                console.error('خطأ في تحليل الإحصائيات:', error);
            }
        }

        // تحميل الإحصائيات
        function loadStatistics() {
            if (dataEngine) {
                addLogEntry('🔄 جاري تحديث الإحصائيات...', 'progress', new Date().toLocaleTimeString('ar-EG'));
                const stats = dataEngine.getDatabaseStatistics();
                updateStatistics(stats);
            }
        }

        // التحقق من توفر pandas
        function checkPandasAvailability() {
            if (dataEngine) {
                const available = dataEngine.checkPandasAvailability();
                if (!available) {
                    addLogEntry('⚠️ تحذير: مكتبة pandas غير متوفرة. لن تعمل وظائف استيراد البيانات من Excel.', 'warning', new Date().toLocaleTimeString('ar-EG'));
                    addLogEntry('💡 لتثبيت pandas، استخدم الأمر: pip install pandas', 'info', new Date().toLocaleTimeString('ar-EG'));
                } else {
                    addLogEntry('✅ مكتبة pandas متوفرة ومجهزة للاستخدام', 'success', new Date().toLocaleTimeString('ar-EG'));
                }
            }
        }

        // وظائف الاستيراد
        function importMasarData() {
            if (dataEngine) {
                addLogEntry('🔄 بدء استيراد بيانات منظومة مسار...', 'progress', new Date().toLocaleTimeString('ar-EG'));
                dataEngine.selectMasarFile();
            } else {
                addLogEntry('❌ خطأ: النظام غير جاهز بعد', 'error', new Date().toLocaleTimeString('ar-EG'));
            }
        }

        function importTeachers() {
            if (dataEngine) {
                addLogEntry('🔄 بدء استيراد أسماء الأساتذة...', 'progress', new Date().toLocaleTimeString('ar-EG'));
                dataEngine.selectTeachersFile();
            } else {
                addLogEntry('❌ خطأ: النظام غير جاهز بعد', 'error', new Date().toLocaleTimeString('ar-EG'));
            }
        }

        function importSecretCodes() {
            if (dataEngine) {
                addLogEntry('🔄 بدء استيراد الرموز السرية...', 'progress', new Date().toLocaleTimeString('ar-EG'));
                dataEngine.selectSecretCodesFiles();
            } else {
                addLogEntry('❌ خطأ: النظام غير جاهز بعد', 'error', new Date().toLocaleTimeString('ar-EG'));
            }
        }

        function importArrivals() {
            if (dataEngine) {
                addLogEntry('🔄 بدء استيراد بيانات الوافدين والمغادرين...', 'progress', new Date().toLocaleTimeString('ar-EG'));
                dataEngine.importArrivalsData();
            } else {
                addLogEntry('❌ خطأ: النظام غير جاهز بعد', 'error', new Date().toLocaleTimeString('ar-EG'));
            }
        }

        function refreshData() {
            if (dataEngine) {
                addLogEntry('🔄 جاري تحديث البيانات...', 'progress', new Date().toLocaleTimeString('ar-EG'));
                dataEngine.refreshData();

                // تحديث الإحصائيات أيضاً
                setTimeout(() => {
                    loadStatistics();
                }, 500);
            } else {
                addLogEntry('❌ خطأ: النظام غير جاهز بعد', 'error', new Date().toLocaleTimeString('ar-EG'));
            }
        }

        function showHelp() {
            if (dataEngine) {
                dataEngine.showHelpGuide();
            } else {
                addLogEntry('❌ خطأ: النظام غير جاهز بعد', 'error', new Date().toLocaleTimeString('ar-EG'));
            }
        }

        function returnToMain() {
            if (typeof importWindow !== 'undefined' && importWindow) {
                importWindow.returnToMainWindow();
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌟 تحميل نظام استيراد البيانات الحديث...');
            initializeChannel();
        });

        console.log('🚀 تم تحميل واجهة استيراد البيانات بنجاح!');
    </script>
</body>
</html>
        """

def main():
    """تشغيل التطبيق"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("نظام استيراد البيانات الحديث")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Modern Education Systems")

    # إنشاء النافذة الرئيسية
    window = ModernSub1Window()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
