"""
نموذج لتحويل نافذة الإعدادات إلى النظام المختلط (Python + HTML)
مثال على كيفية تطبيق المنهجية الجديدة
"""

import sys
import os
import json
import sqlite3
import shutil
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QFileDialog, QMessageBox
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, Qt
from PyQt5.QtGui import QIcon

class SettingsEngine(QObject):
    """محرك إعدادات النظام - مسؤول عن إدارة الإعدادات والنسخ الاحتياطي"""
    
    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    operationCompleted = pyqtSignal(str, bool)  # operation_name, success
    settingsUpdated = pyqtSignal(str)  # settings JSON
    
    def __init__(self):
        super().__init__()
        self.db_path = "data.db"
        self.backup_folder = "backups"
        self.settings_file = "app_settings.json"
        
        # إنشاء مجلد النسخ الاحتياطي
        if not os.path.exists(self.backup_folder):
            os.makedirs(self.backup_folder)
    
    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logUpdated.emit(message, status, timestamp)
    
    @pyqtSlot(result=str)
    def getSystemInfo(self):
        """الحصول على معلومات النظام"""
        try:
            info = {
                "database_path": self.db_path,
                "database_exists": os.path.exists(self.db_path),
                "database_size": self.get_file_size(self.db_path) if os.path.exists(self.db_path) else 0,
                "backup_folder": self.backup_folder,
                "backup_count": len([f for f in os.listdir(self.backup_folder) if f.endswith('.db')]) if os.path.exists(self.backup_folder) else 0,
                "settings_file": self.settings_file,
                "settings_exists": os.path.exists(self.settings_file)
            }
            return json.dumps(info, ensure_ascii=False)
        except Exception as e:
            self.emit_log(f"خطأ في جمع معلومات النظام: {str(e)}", "error")
            return json.dumps({"error": str(e)}, ensure_ascii=False)
    
    def get_file_size(self, file_path):
        """الحصول على حجم الملف بالميجابايت"""
        try:
            size_bytes = os.path.getsize(file_path)
            size_mb = round(size_bytes / (1024 * 1024), 2)
            return size_mb
        except:
            return 0
    
    @pyqtSlot()
    def createBackup(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        self.emit_log("بدء إنشاء النسخة الاحتياطية...", "progress")
        
        try:
            if not os.path.exists(self.db_path):
                self.emit_log("قاعدة البيانات غير موجودة", "error")
                self.operationCompleted.emit("backup", False)
                return
            
            # إنشاء اسم الملف مع التاريخ والوقت
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"backup_{timestamp}.db"
            backup_path = os.path.join(self.backup_folder, backup_filename)
            
            # نسخ قاعدة البيانات
            shutil.copy2(self.db_path, backup_path)
            
            # التحقق من نجاح النسخ
            if os.path.exists(backup_path):
                size_mb = self.get_file_size(backup_path)
                self.emit_log(f"✅ تم إنشاء النسخة الاحتياطية بنجاح: {backup_filename} ({size_mb} MB)", "success")
                self.operationCompleted.emit("backup", True)
            else:
                self.emit_log("فشل في إنشاء النسخة الاحتياطية", "error")
                self.operationCompleted.emit("backup", False)
                
        except Exception as e:
            self.emit_log(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}", "error")
            self.operationCompleted.emit("backup", False)
    
    @pyqtSlot()
    def restoreBackup(self):
        """استعادة نسخة احتياطية"""
        try:
            # فتح نافذة لاختيار ملف النسخة الاحتياطية
            file_path, _ = QFileDialog.getOpenFileName(
                None, "اختر ملف النسخة الاحتياطية", self.backup_folder, "Database Files (*.db)"
            )
            
            if file_path:
                self.performRestore(file_path)
        except Exception as e:
            self.emit_log(f"خطأ في اختيار ملف الاستعادة: {str(e)}", "error")
    
    def performRestore(self, backup_path):
        """تنفيذ عملية الاستعادة"""
        self.emit_log("بدء استعادة النسخة الاحتياطية...", "progress")
        
        try:
            if not os.path.exists(backup_path):
                self.emit_log("ملف النسخة الاحتياطية غير موجود", "error")
                return
            
            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
            if os.path.exists(self.db_path):
                current_backup = f"current_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                current_backup_path = os.path.join(self.backup_folder, current_backup)
                shutil.copy2(self.db_path, current_backup_path)
                self.emit_log(f"تم حفظ نسخة احتياطية من قاعدة البيانات الحالية: {current_backup}", "info")
            
            # استعادة النسخة الاحتياطية
            shutil.copy2(backup_path, self.db_path)
            
            # التحقق من نجاح الاستعادة
            if os.path.exists(self.db_path):
                size_mb = self.get_file_size(self.db_path)
                self.emit_log(f"✅ تم استعادة النسخة الاحتياطية بنجاح ({size_mb} MB)", "success")
                self.operationCompleted.emit("restore", True)
            else:
                self.emit_log("فشل في استعادة النسخة الاحتياطية", "error")
                self.operationCompleted.emit("restore", False)
                
        except Exception as e:
            self.emit_log(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}", "error")
            self.operationCompleted.emit("restore", False)
    
    @pyqtSlot()
    def cleanDatabase(self):
        """تنظيف قاعدة البيانات"""
        self.emit_log("بدء تنظيف قاعدة البيانات...", "progress")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # تنظيف البيانات المكررة والفارغة
            cleanup_queries = [
                "DELETE FROM اللوائح WHERE الرمز IS NULL OR TRIM(الرمز) = ''",
                "DELETE FROM السجل_العام WHERE الرمز IS NULL OR TRIM(الرمز) = ''",
                "DELETE FROM الأساتذة WHERE اسم_الأستاذ IS NULL OR TRIM(اسم_الأستاذ) = ''",
                "VACUUM"  # ضغط قاعدة البيانات
            ]
            
            for query in cleanup_queries:
                cursor.execute(query)
                if cursor.rowcount > 0:
                    self.emit_log(f"تم حذف {cursor.rowcount} سجل", "info")
            
            conn.commit()
            conn.close()
            
            self.emit_log("✅ تم تنظيف قاعدة البيانات بنجاح", "success")
            self.operationCompleted.emit("cleanup", True)
            
        except Exception as e:
            self.emit_log(f"خطأ في تنظيف قاعدة البيانات: {str(e)}", "error")
            self.operationCompleted.emit("cleanup", False)
    
    @pyqtSlot()
    def resetForNewSeason(self):
        """تهيئة النظام لموسم دراسي جديد"""
        self.emit_log("بدء تهيئة النظام للموسم الجديد...", "progress")
        
        try:
            # إنشاء نسخة احتياطية أولاً
            self.createBackup()
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # حذف بيانات الموسم السابق (مع الاحتفاظ بالهيكل)
            reset_queries = [
                "DELETE FROM اللوائح",
                "DELETE FROM الرمز_السري", 
                "DELETE FROM 'السجل الاولي'",
                "UPDATE بيانات_المؤسسة SET السنة_الدراسية = NULL"
            ]
            
            for query in reset_queries:
                try:
                    cursor.execute(query)
                    self.emit_log(f"تم تنفيذ: {query}", "info")
                except sqlite3.OperationalError:
                    # الجدول غير موجود، تجاهل الخطأ
                    pass
            
            conn.commit()
            conn.close()
            
            self.emit_log("✅ تم تهيئة النظام للموسم الجديد بنجاح", "success")
            self.operationCompleted.emit("reset", True)
            
        except Exception as e:
            self.emit_log(f"خطأ في تهيئة النظام: {str(e)}", "error")
            self.operationCompleted.emit("reset", False)

class ModernSettingsWindow(QMainWindow):
    """نافذة إعدادات النظام الحديثة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("⚙️ إعدادات النظام")
        self.setFixedSize(900, 700)
        
        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # إنشاء محرك الإعدادات
        self.settings_engine = SettingsEngine()
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء عرض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)
        
        # تحميل واجهة HTML
        html_content = self.get_html_interface()
        self.web_view.setHtml(html_content)
    
    def setup_web_channel(self):
        """إعداد قناة التواصل مع JavaScript"""
        self.channel = QWebChannel()
        self.channel.registerObject("settingsEngine", self.settings_engine)
        self.web_view.page().setWebChannel(self.channel)
        
        # انتظار تحميل الصفحة قبل إعداد القناة
        self.web_view.loadFinished.connect(self.on_page_loaded)
    
    def on_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة"""
        # إعادة تسجيل الكائن للتأكد من الربط
        self.channel.registerObject("settingsEngine", self.settings_engine)

    def get_html_interface(self):
        """إنشاء واجهة HTML لإعدادات النظام"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Calibri', 'Tahoma', 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 95%;
            margin: 20px auto;
            display: grid;
            grid-template-rows: auto 1fr;
            gap: 20px;
            height: calc(100vh - 40px);
        }

        /* شريط العنوان */
        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #667eea;
            font-size: 24px;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 14px;
        }

        /* المحتوى الرئيسي */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        /* لوحة العمليات */
        .operations-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .panel-title {
            color: #667eea;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .operations-grid {
            display: grid;
            gap: 15px;
        }

        .operation-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .operation-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .operation-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .operation-description {
            font-size: 13px;
            color: #6c757d;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .operation-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-size: 13px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .operation-button:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a4190);
            transform: scale(1.02);
        }

        .operation-button.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .operation-button.danger:hover {
            background: linear-gradient(135deg, #d62c1a, #a93226);
        }

        .operation-button.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .operation-button.warning:hover {
            background: linear-gradient(135deg, #e67e22, #d35400);
        }

        /* لوحة السجلات */
        .log-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }

        .log-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.6;
            flex: 1;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 5px 10px;
            border-radius: 5px;
            display: flex;
            align-items: center;
        }

        .log-entry.info {
            background: rgba(52, 152, 219, 0.1);
            border-left: 4px solid #3498db;
        }

        .log-entry.success {
            background: rgba(46, 204, 113, 0.1);
            border-left: 4px solid #2ecc71;
        }

        .log-entry.error {
            background: rgba(231, 76, 60, 0.1);
            border-left: 4px solid #e74c3c;
        }

        .log-entry.warning {
            background: rgba(243, 156, 18, 0.1);
            border-left: 4px solid #f39c12;
        }

        .log-entry.progress {
            background: rgba(155, 89, 182, 0.1);
            border-left: 4px solid #9b59b6;
        }

        .log-timestamp {
            color: #7f8c8d;
            margin-left: auto;
            font-size: 11px;
        }

        /* معلومات النظام */
        .system-info {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .info-label {
            font-weight: bold;
            color: #2c3e50;
        }

        .info-value {
            color: #34495e;
        }

        /* تأثيرات التحميل */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #667eea;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- شريط العنوان -->
        <div class="header">
            <h1>⚙️ إعدادات النظام</h1>
            <p>إدارة النسخ الاحتياطي، الصيانة، وتهيئة النظام</p>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <!-- لوحة العمليات -->
            <div class="operations-panel">
                <div class="panel-title">
                    🔧 عمليات النظام
                </div>

                <!-- معلومات النظام -->
                <div class="system-info" id="systemInfo">
                    <div class="info-item">
                        <span class="info-label">قاعدة البيانات:</span>
                        <span class="info-value" id="dbPath">جاري التحميل...</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">حجم قاعدة البيانات:</span>
                        <span class="info-value" id="dbSize">جاري التحميل...</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">عدد النسخ الاحتياطية:</span>
                        <span class="info-value" id="backupCount">جاري التحميل...</span>
                    </div>
                </div>

                <div class="operations-grid">
                    <!-- إنشاء نسخة احتياطية -->
                    <div class="operation-card">
                        <div class="operation-title">💾 إنشاء نسخة احتياطية</div>
                        <div class="operation-description">
                            إنشاء نسخة احتياطية من قاعدة البيانات الحالية مع التاريخ والوقت
                        </div>
                        <button class="operation-button" onclick="createBackup()">
                            💾 إنشاء نسخة احتياطية
                        </button>
                    </div>

                    <!-- استعادة نسخة احتياطية -->
                    <div class="operation-card">
                        <div class="operation-title">📥 استعادة نسخة احتياطية</div>
                        <div class="operation-description">
                            استعادة قاعدة البيانات من نسخة احتياطية سابقة
                        </div>
                        <button class="operation-button warning" onclick="restoreBackup()">
                            📥 استعادة نسخة احتياطية
                        </button>
                    </div>

                    <!-- تنظيف قاعدة البيانات -->
                    <div class="operation-card">
                        <div class="operation-title">🧹 تنظيف قاعدة البيانات</div>
                        <div class="operation-description">
                            حذف البيانات المكررة والفارغة وضغط قاعدة البيانات
                        </div>
                        <button class="operation-button" onclick="cleanDatabase()">
                            🧹 تنظيف قاعدة البيانات
                        </button>
                    </div>

                    <!-- تهيئة موسم جديد -->
                    <div class="operation-card">
                        <div class="operation-title">🔄 تهيئة موسم دراسي جديد</div>
                        <div class="operation-description">
                            حذف بيانات الموسم السابق وتهيئة النظام لموسم جديد
                        </div>
                        <button class="operation-button danger" onclick="resetForNewSeason()">
                            🔄 تهيئة موسم جديد
                        </button>
                    </div>
                </div>
            </div>

            <!-- لوحة السجلات -->
            <div class="log-panel">
                <div class="panel-title">
                    📋 سجل العمليات
                </div>

                <div class="log-container" id="logContainer">
                    <!-- سيتم إضافة السجلات هنا ديناميكياً -->
                </div>
            </div>
        </div>
    </div>

    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <script>
        let settingsEngine = null;
        let isChannelReady = false;

        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    settingsEngine = channel.objects.settingsEngine;
                    isChannelReady = true;

                    console.log('🚀 QWebChannel initialized successfully');

                    // ربط الإشارات
                    if (settingsEngine) {
                        settingsEngine.logUpdated.connect(addLogEntry);
                        settingsEngine.operationCompleted.connect(onOperationCompleted);
                        settingsEngine.settingsUpdated.connect(updateSettings);

                        // تحميل معلومات النظام
                        loadSystemInfo();

                        // رسالة ترحيبية
                        addLogEntry('أهلاً بك في إعدادات النظام', 'info', new Date().toLocaleTimeString('ar-EG'));
                        addLogEntry('يمكنك إدارة النسخ الاحتياطي وصيانة النظام من هنا', 'info', new Date().toLocaleTimeString('ar-EG'));
                    }
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // تحميل معلومات النظام
        function loadSystemInfo() {
            if (settingsEngine) {
                const systemInfo = JSON.parse(settingsEngine.getSystemInfo());

                document.getElementById('dbPath').textContent = systemInfo.database_path || 'غير محدد';
                document.getElementById('dbSize').textContent = systemInfo.database_size ?
                    systemInfo.database_size + ' MB' : 'غير متوفر';
                document.getElementById('backupCount').textContent = systemInfo.backup_count || '0';
            }
        }

        // إضافة إدخال إلى السجل
        function addLogEntry(message, status, timestamp) {
            const logContainer = document.getElementById('logContainer');
            const entry = document.createElement('div');
            entry.className = `log-entry ${status}`;

            const statusIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️',
                'progress': '🔄'
            }[status] || 'ℹ️';

            entry.innerHTML = `
                <span>${statusIcon} ${message}</span>
                <span class="log-timestamp">${timestamp}</span>
            `;

            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // معالجة اكتمال العمليات
        function onOperationCompleted(operationName, success) {
            // تحديث معلومات النظام بعد العمليات
            setTimeout(() => {
                loadSystemInfo();
            }, 1000);
        }

        // وظائف العمليات
        function createBackup() {
            if (settingsEngine) {
                addLogEntry('🔄 بدء إنشاء النسخة الاحتياطية...', 'progress', new Date().toLocaleTimeString('ar-EG'));
                settingsEngine.createBackup();
            } else {
                addLogEntry('❌ خطأ: النظام غير جاهز بعد', 'error', new Date().toLocaleTimeString('ar-EG'));
            }
        }

        function restoreBackup() {
            if (settingsEngine) {
                if (confirm('هل أنت متأكد من استعادة نسخة احتياطية؟ سيتم استبدال البيانات الحالية.')) {
                    addLogEntry('🔄 بدء استعادة النسخة الاحتياطية...', 'progress', new Date().toLocaleTimeString('ar-EG'));
                    settingsEngine.restoreBackup();
                }
            } else {
                addLogEntry('❌ خطأ: النظام غير جاهز بعد', 'error', new Date().toLocaleTimeString('ar-EG'));
            }
        }

        function cleanDatabase() {
            if (settingsEngine) {
                if (confirm('هل تريد تنظيف قاعدة البيانات؟ سيتم حذف البيانات المكررة والفارغة.')) {
                    addLogEntry('🔄 بدء تنظيف قاعدة البيانات...', 'progress', new Date().toLocaleTimeString('ar-EG'));
                    settingsEngine.cleanDatabase();
                }
            } else {
                addLogEntry('❌ خطأ: النظام غير جاهز بعد', 'error', new Date().toLocaleTimeString('ar-EG'));
            }
        }

        function resetForNewSeason() {
            if (settingsEngine) {
                const confirmMessage = 'تحذير: هذه العملية ستحذف جميع بيانات الموسم الحالي!\\n\\nهل أنت متأكد من تهيئة النظام لموسم دراسي جديد؟';
                if (confirm(confirmMessage)) {
                    addLogEntry('🔄 بدء تهيئة النظام للموسم الجديد...', 'progress', new Date().toLocaleTimeString('ar-EG'));
                    settingsEngine.resetForNewSeason();
                }
            } else {
                addLogEntry('❌ خطأ: النظام غير جاهز بعد', 'error', new Date().toLocaleTimeString('ar-EG'));
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌟 تحميل إعدادات النظام...');
            initializeChannel();
        });

        console.log('🚀 تم تحميل واجهة إعدادات النظام بنجاح!');
    </script>
</body>
</html>
        """

def main():
    """تشغيل نافذة الإعدادات"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("إعدادات النظام")
    app.setApplicationVersion("2.0")

    # إنشاء النافذة
    window = ModernSettingsWindow()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
