/*
ملف الأنماط الرئيسي للنظام التعليمي الشامل
تم فصله لتحسين الأداء وسهولة الصيانة
*/

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', 'Ta<PERSON>a', 'Arial', sans-serif;
    font-size: 14px;
    font-weight: bold;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
    direction: rtl;
}

.main-container {
    max-width: 1200px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: white;
    padding: 40px;
    text-align: center;
}

.header h1 {
    font-size: 36px;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 18px;
    opacity: 0.95;
}

.status-bar {
    background: linear-gradient(135deg, #e9ecef, #f8f9fa);
    padding: 15px 30px;
    font-size: 13px;
    color: #495057;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #28a745;
}

.dashboard-content {
    padding: 40px;
}

.quick-access {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
}

.quick-access h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.quick-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.quick-link {
    background: white;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    padding: 12px 20px;
    text-decoration: none;
    color: #495057;
    font-size: 13px;
    font-weight: bold;
    transition: all 0.3s ease;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.quick-link:hover {
    background: #f8f9fa;
    border-color: #9b59b6;
    color: #9b59b6;
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(155, 89, 182, 0.2);
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin: 30px 0;
}

.stat-item {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.stat-value {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.stat-label {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 600;
}

.footer {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    text-align: center;
    padding: 30px;
    font-size: 13px;
}

.footer p {
    margin-bottom: 5px;
    opacity: 0.9;
}

/* تجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .quick-links {
        grid-template-columns: 1fr;
    }
    
    .header h1 {
        font-size: 28px;
    }
    
    .dashboard-content {
        padding: 20px;
    }
    
    body {
        padding: 10px;
    }
}

/* تحسينات الأداء المبسطة */
.main-container {
    position: relative;
}

/* إزالة جميع التأثيرات المتحركة */
* {
    animation: none !important;
    transition: none !important;
}

/* استثناءات للتأثيرات البسيطة فقط */
.quick-link, .stat-item, .module-card, .module-button {
    transition: all 0.2s ease !important;
}

/* أنماط الوحدات الإضافية */
.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.module-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-color: #9b59b6;
}

.module-icon {
    font-size: 60px;
    margin-bottom: 20px;
    display: block;
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
}

.module-card:hover .module-icon {
    transform: scale(1.05);
}

.module-title {
    font-size: 22px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
}

.module-description {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 25px;
    line-height: 1.6;
    position: relative;
    z-index: 1;
}

.module-button {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 15px 30px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.module-button:hover {
    background: linear-gradient(135deg, #8e44ad, #7d3c98);
    transform: scale(1.02);
    box-shadow: 0 3px 10px rgba(155, 89, 182, 0.3);
}

.module-button.secondary {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.module-button.secondary:hover {
    background: linear-gradient(135deg, #2980b9, #1f618d);
}

.module-button.success {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.module-button.success:hover {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.module-button.warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.module-button.warning:hover {
    background: linear-gradient(135deg, #e67e22, #d35400);
}
