<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام التعليمي الشامل</title>
    
    <!-- تضمين نظام إدارة الخط والألوان -->
    <link rel="stylesheet" href="dynamic-styles.css">
    <script src="font-color-manager.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>a', 'Arial', sans-serif;
            font-size: 14px;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .dashboard-content {
            padding: 40px;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .module-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #9b59b6;
        }

        .module-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }

        .module-title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .module-description {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .module-button {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 25px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .module-button:hover {
            background: linear-gradient(135deg, #8e44ad, #7d3c98);
            transform: scale(1.05);
        }

        .module-button.secondary {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .module-button.secondary:hover {
            background: linear-gradient(135deg, #2980b9, #1f618d);
        }

        .quick-access {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .quick-access h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .quick-links {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .quick-link {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 8px 15px;
            text-decoration: none;
            color: #495057;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .quick-link:hover {
            background: #e9ecef;
            border-color: #9b59b6;
            color: #9b59b6;
        }

        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 12px;
        }

        .status-bar {
            background: #e9ecef;
            padding: 10px 20px;
            font-size: 12px;
            color: #6c757d;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #28a745;
            margin-left: 5px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <h1>🎓 النظام التعليمي الشامل</h1>
            <p>منظومة متكاملة لإدارة البيانات التعليمية والطلابية</p>
        </div>

        <div class="status-bar">
            <span>
                <span class="status-indicator"></span>
                النظام متصل ويعمل بشكل طبيعي
            </span>
            <span id="current-time"></span>
        </div>

        <div class="dashboard-content">
            <div class="quick-access">
                <h3>🚀 وصول سريع</h3>                <div class="quick-links">
                    <a href="main_window1.html" class="quick-link">📥 نظام الاستيراد</a>
                    <a href="index.html#students" class="quick-link">👨‍🎓 إدارة الطلاب</a>
                    <a href="index.html#reports" class="quick-link">📊 التقارير</a>
                    <a href="index.html#institution" class="quick-link">🏢 بيانات المؤسسة</a>
                    <a href="main_window4.html" class="quick-link">📊 الإحصائيات</a>
                    <a href="main_window5.html" class="quick-link">📝 المستندات</a>
                    <a href="main_window7.html" class="quick-link">🎨 إعدادات التصميم</a>
                    <a href="printer-settings.html" class="quick-link">🖨️ الطابعة</a>
                </div>
            </div>

            <div class="modules-grid">                <!-- وحدة إعدادات الخط والألوان -->
                <div class="module-card" onclick="openModule('main_window7.html')">
                    <span class="module-icon">🎨</span>
                    <div class="module-title">إعدادات الخط والألوان</div>
                    <div class="module-description">
                        تخصيص خطوط وألوان البرنامج، ضبط أحجام النصوص، وتنسيق الواجهات
                        <br><strong>الميزات:</strong> خطوط العناوين، خطوط الجداول، الألوان، التنسيقات
                    </div>
                    <a href="main_window7.html" class="module-button">🎨 إعدادات التصميم</a>
                </div>

                <!-- وحدة استيراد البيانات -->
                <div class="module-card" onclick="openModule('main_window1.html')">
                    <span class="module-icon">📥</span>
                    <div class="module-title">نظام الاستيراد</div>
                    <div class="module-description">
                        استيراد اللوائح من منظومة مسار، الرموز السرية، وأسماء الأساتذة والمواد المدرسة
                        <br><strong>الميزات:</strong> معالجة متقدمة، تحكم في الخط، تقارير مفصلة
                    </div>
                    <a href="main_window1.html" class="module-button">🚀 فتح نظام الاستيراد</a>
                </div>                <!-- وحدة إعدادات البرنامج -->
                <div class="module-card" onclick="openModule('main_window2.html')">
                    <span class="module-icon">⚙️</span>
                    <div class="module-title">إعدادات البرنامج</div>
                    <div class="module-description">
                        إدارة إعدادات النظام، النسخ الاحتياطي، الاستعادة، وصيانة قاعدة البيانات
                        <br><strong>الميزات:</strong> نسخ احتياطي، تهيئة موسم جديد، حذف البيانات
                    </div>
                    <a href="main_window2.html" class="module-button secondary">⚙️ فتح إعدادات البرنامج</a>
                </div>                <!-- وحدة بيانات المؤسسة -->
                <div class="module-card" onclick="openModule('main_window3.html')">
                    <span class="module-icon">🏢</span>
                    <div class="module-title">بيانات المؤسسة</div>
                    <div class="module-description">
                        إدارة بيانات المؤسسة التعليمية، المعلومات الأساسية، والشعار الرسمي
                        <br><strong>الميزات:</strong> تحديد اسم المؤسسة، العنوان، الشعار، معلومات الاتصال
                    </div>
                    <a href="main_window3.html" class="module-button">🏢 إدارة بيانات المؤسسة</a>
                </div>                <!-- وحدة الإحصائيات -->
                <div class="module-card" onclick="openModule('main_window4.html')">
                    <span class="module-icon">📊</span>
                    <div class="module-title">الإحصائيات</div>
                    <div class="module-description">
                        تقارير شاملة وإحصائيات مفصلة للطلاب، الدرجات، والأداء الأكاديمي
                        <br><strong>الميزات:</strong> رسوم بيانية، تقارير دورية، تحليل البيانات
                    </div>
                    <a href="main_window4.html" class="module-button">📊 عرض الإحصائيات</a>
                </div>                <!-- وحدة عناوين الأوراق والملاحظات -->
                <div class="module-card" onclick="openModule('main_window5.html')">
                    <span class="module-icon">📝</span>
                    <div class="module-title">عناوين الأوراق والملاحظات</div>
                    <div class="module-description">
                        إدارة عناوين المستندات، الأوراق الرسمية، والملاحظات الإدارية
                        <br><strong>الميزات:</strong> قوالب جاهزة، تخصيص العناوين، حفظ الملاحظات
                    </div>
                    <a href="main_window5.html" class="module-button">📝 إدارة المستندات</a>
                </div>

                <!-- وحدة إعدادات الطابعة -->
                <div class="module-card" onclick="openModule('printer-settings.html')">
                    <span class="module-icon">🖨️</span>
                    <div class="module-title">إعدادات الطابعة</div>
                    <div class="module-description">
                        تكوين إعدادات الطباعة، اختيار الطابعة الافتراضية، وضبط جودة الطباعة
                        <br><strong>الميزات:</strong> اختيار الطابعة، حجم الورق، جودة الطباعة
                    </div>
                    <a href="printer-settings.html" class="module-button">🖨️ إعدادات الطباعة</a>
                </div>

                <!-- وحدة الاتصال بالخادم وتشغيله -->
                <div class="module-card">
                    <span class="module-icon">🔗</span>
                    <div class="module-title">الاتصال بالخادم وتشغيله</div>
                    <div class="module-description">
                        مراقبة حالة الخادم، إدارة الاتصال، وتشغيل الخدمات الأساسية
                        <br><strong>الحالة:</strong> <span id="server-status-2">جاري التحقق...</span>
                    </div>
                    <button class="module-button" onclick="manageServer()">🔗 إدارة الخادم</button>
                </div>

                <!-- وحدة المساعدة والدعم -->
                <div class="module-card">
                    <span class="module-icon">❓</span>
                    <div class="module-title">المساعدة والدعم</div>
                    <div class="module-description">
                        أدلة الاستخدام، الأسئلة الشائعة، ومعلومات التقنية
                        <br><strong>متوفر:</strong> أدلة PDF، فيديوهات تعليمية
                    </div>
                    <button class="module-button" onclick="showHelp()">📖 عرض المساعدة</button>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2025 النظام التعليمي الشامل - تم التطوير بواسطة فريق التقنية التعليمية</p>
            <p>الإصدار 2.0 | آخر تحديث: يونيو 2025</p>
        </div>
    </div>

    <script>
        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric', 
                hour: '2-digit', 
                minute: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }

        // فتح وحدة في نافذة جديدة أو نفس النافذة
        function openModule(url) {
            // يمكنك اختيار أحد الخيارين:
            // window.open(url, '_blank'); // فتح في نافذة جديدة
            window.location.href = url; // فتح في نفس النافذة
        }        // فحص حالة الخادم
        async function checkServerStatus() {
            const statusElement = document.getElementById('server-status');
            statusElement.textContent = 'جاري الفحص...';
            
            try {
                // محاولة الاتصال بالخادم الموحد
                const response = await fetch('http://localhost:5000/api/check-status');
                if (response.ok) {
                    const data = await response.json();
                    statusElement.textContent = 'متصل ويعمل ✅';
                    statusElement.style.color = '#28a745';
                } else {
                    statusElement.textContent = 'غير متصل ❌';
                    statusElement.style.color = '#dc3545';
                }
            } catch (error) {
                statusElement.textContent = 'خطأ في الاتصال ⚠️';
                statusElement.style.color = '#ffc107';
            }
        }

        // عرض نافذة المساعدة
        function showHelp() {
            alert(`🎓 النظام التعليمي الشامل - المساعدة

📥 نظام استيراد البيانات:
- استيراد اللوائح من منظومة مسار
- استيراد الرموز السرية دفعة واحدة
- استيراد أسماء الأساتذة والمواد

🏢 النظام الرئيسي:
- إدارة بيانات المؤسسة
- إدارة الطلاب والتسجيل
- التقارير والإحصائيات
- الإدارة المالية

🔧 المتطلبات:
- تشغيل الخادم: python unified_server.py
- ملف قاعدة البيانات: data.db
- متصفح يدعم JavaScript

💡 نصائح:
- احفظ نسخة احتياطية قبل الاستيراد
- تأكد من اتصال الخادم قبل البدء
- استخدم ملفات Excel بالتنسيق الصحيح`);
        }

        // إدارة الخادم
        async function manageServer() {
            const serverStatusElement = document.getElementById('server-status-2');
            
            try {
                const response = await fetch('http://localhost:5000/api/check-status');
                if (response.ok) {
                    const data = await response.json();
                    
                    // عرض معلومات مفصلة عن الخادم
                    const serverInfo = `🖥️ معلومات الخادم:

✅ الحالة: متصل ويعمل بشكل طبيعي
📍 العنوان: http://localhost:5000
🗄️ قاعدة البيانات: ${data.database_file || 'data.db'}
📊 عدد الجداول: ${data.total_tables || 'غير محدد'}

🔧 الوظائف المتاحة:
- استيراد البيانات
- النسخ الاحتياطي
- إدارة قاعدة البيانات
- الصيانة والتنظيف

💡 للتشغيل اليدوي:
python unified_server.py`;

                    alert(serverInfo);
                    
                    serverStatusElement.textContent = 'متصل ✅';
                    serverStatusElement.style.color = '#28a745';
                } else {
                    throw new Error('الخادم غير متاح');
                }
            } catch (error) {
                const serverError = `⚠️ خطأ في الاتصال بالخادم:

❌ الحالة: غير متصل
🔍 السبب المحتمل:
- الخادم غير مشغل
- مشكلة في الشبكة
- منفذ 5000 مستخدم

🔧 الحلول:
1. تشغيل الخادم: python unified_server.py
2. التأكد من تثبيت المتطلبات
3. فحص منفذ الشبكة

💡 نصيحة: تأكد من تشغيل الخادم أولاً`;

                alert(serverError);
                
                serverStatusElement.textContent = 'غير متصل ❌';
                serverStatusElement.style.color = '#dc3545';
            }
        }        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 60000); // تحديث كل دقيقة
            
            // فحص حالة الخادم تلقائياً
            setTimeout(checkServerStatus, 1000);
            setTimeout(checkServerStatusForCard, 1500); // فحص إضافي للبطاقة الجديدة
        });

        // فحص حالة الخادم للبطاقة الجديدة
        async function checkServerStatusForCard() {
            const serverStatusElement = document.getElementById('server-status-2');
            
            try {
                const response = await fetch('http://localhost:5000/api/check-status');
                if (response.ok) {
                    serverStatusElement.textContent = 'متصل ويعمل ✅';
                    serverStatusElement.style.color = '#28a745';
                } else {
                    serverStatusElement.textContent = 'غير متصل ❌';
                    serverStatusElement.style.color = '#dc3545';
                }
            } catch (error) {
                serverStatusElement.textContent = 'خطأ في الاتصال ⚠️';
                serverStatusElement.style.color = '#ffc107';
            }
        }

        // تأثيرات التفاعل
        document.querySelectorAll('.module-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.borderColor = '#9b59b6';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.borderColor = '#e9ecef';
            });
        });
    </script>
</body>
</html>
