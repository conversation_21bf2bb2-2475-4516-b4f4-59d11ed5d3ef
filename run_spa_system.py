"""
النظام التعليمي الشامل الحديث - ملف واحد شامل
نظام الصفحات الموحد مثل صفحات الإنترنت الحديثة

الميزات:
- ملف واحد فقط - لا حاجة لملفات أخرى
- نظام صفحات موحد مثل المواقع الحديثة
- تنقل سلس بين الصفحات
- شريط تنقل علوي ثابت
- بدون تموجات أو تأثيرات متحركة
- أداء محسن ومستقر
"""

import sys
import os
import json
import sqlite3
import requests
from datetime import datetime
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QFileDialog, QHBoxLayout
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, QTimer, QDateTime, Qt
from PyQt5.QtGui import QIcon

# محاولة استيراد pandas مع معالجة الخطأ
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

# استيراد نافذة الاستيراد
try:
    from sub1_window import ModernSub1Window
    SUB1_WINDOW_AVAILABLE = True
except ImportError:
    SUB1_WINDOW_AVAILABLE = False

class SystemEngine(QObject):
    """محرك النظام الرئيسي - مسؤول عن إدارة النظام والصفحات"""

    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    systemStatusUpdated = pyqtSignal(str)  # system status JSON
    pageChanged = pyqtSignal(str)  # page_name
    serverStatusUpdated = pyqtSignal(bool, str)  # is_connected, info

    def __init__(self):
        super().__init__()
        self.db_path = "data.db"
        self.server_url = "http://localhost:5000"
        self.current_page = "home"
        self.main_window = None  # مرجع للنافذة الرئيسية

        # مؤقت لفحص حالة الخادم
        self.server_check_timer = QTimer()
        self.server_check_timer.timeout.connect(self.check_server_status)
        self.server_check_timer.start(30000)  # فحص كل 30 ثانية

    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logUpdated.emit(message, status, timestamp)

    @pyqtSlot(result=str)
    def getSystemStatus(self):
        """الحصول على حالة النظام الشاملة"""
        try:
            status = {
                "database": {
                    "exists": os.path.exists(self.db_path),
                    "size_mb": self.get_file_size(self.db_path) if os.path.exists(self.db_path) else 0,
                    "tables_count": self.get_tables_count()
                },
                "modules": {
                    "import_system": True,
                    "settings_system": True,
                    "statistics_system": True,
                    "documents_system": True
                },
                "server": {
                    "status": "checking",
                    "url": self.server_url
                },
                "system_info": {
                    "version": "2.0 - ملف واحد شامل",
                    "last_update": "يونيو 2025",
                    "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
                }
            }
            return json.dumps(status, ensure_ascii=False)
        except Exception as e:
            self.emit_log(f"خطأ في جمع حالة النظام: {str(e)}", "error")
            return json.dumps({"error": str(e)}, ensure_ascii=False)

    def get_file_size(self, file_path):
        """الحصول على حجم الملف بالميجابايت"""
        try:
            size_bytes = os.path.getsize(file_path)
            return round(size_bytes / (1024 * 1024), 2)
        except:
            return 0

    def get_tables_count(self):
        """عدد الجداول في قاعدة البيانات"""
        try:
            if not os.path.exists(self.db_path):
                return 0

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            count = cursor.fetchone()[0]
            conn.close()
            return count
        except:
            return 0

    @pyqtSlot(str)
    def navigateToPage(self, page_name):
        """التنقل إلى صفحة معينة"""
        self.current_page = page_name
        self.emit_log(f"الانتقال إلى صفحة: {page_name}", "info")
        self.pageChanged.emit(page_name)

    @pyqtSlot(result=str)
    def getCurrentPage(self):
        """الحصول على الصفحة الحالية"""
        return self.current_page

    @pyqtSlot()
    def openImportSystem(self):
        """فتح نظام الاستيراد"""
        if self.main_window:
            self.main_window.open_import_window()

    @pyqtSlot()
    def closeImportSystem(self):
        """إغلاق نظام الاستيراد"""
        if self.main_window:
            self.main_window.close_import_window()

    @pyqtSlot()
    def openSettingsSystem(self):
        """فتح نظام الإعدادات"""
        self.emit_log("تم طلب فتح نظام الإعدادات", "info")
        # يمكن إضافة كود لفتح نظام الإعدادات هنا

    @pyqtSlot()
    def check_server_status(self):
        """فحص حالة الخادم"""
        try:
            response = requests.get(f"{self.server_url}/api/check-status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                server_info = {
                    "connected": True,
                    "url": self.server_url,
                    "database_file": data.get("database_file", "data.db"),
                    "total_tables": data.get("total_tables", 0),
                    "status": "متصل ويعمل بشكل طبيعي"
                }
                self.serverStatusUpdated.emit(True, json.dumps(server_info, ensure_ascii=False))
            else:
                raise Exception("Server returned non-200 status")
        except Exception as e:
            server_info = {
                "connected": False,
                "url": self.server_url,
                "error": str(e),
                "status": "غير متصل"
            }
            self.serverStatusUpdated.emit(False, json.dumps(server_info, ensure_ascii=False))

    @pyqtSlot()
    def showSystemHelp(self):
        """عرض مساعدة النظام"""
        help_text = """
🎓 النظام التعليمي الشامل - المساعدة (ملف واحد شامل)

📥 نظام استيراد البيانات:
- استيراد اللوائح من منظومة مسار
- استيراد الرموز السرية دفعة واحدة
- استيراد أسماء الأساتذة والمواد

⚙️ إعدادات النظام:
- النسخ الاحتياطي والاستعادة
- تنظيف قاعدة البيانات
- تهيئة موسم دراسي جديد

✨ مميزات هذه النسخة:
- ملف واحد شامل - لا حاجة لملفات أخرى
- تنقل سلس مثل صفحات الإنترنت
- شريط تنقل علوي ثابت
- أداء محسن بدون تموجات
        """
        self.emit_log(help_text, "info")

    @pyqtSlot(result=str)
    def getDatabaseStatistics(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            if not os.path.exists(self.db_path):
                return json.dumps({"error": "قاعدة البيانات غير موجودة"}, ensure_ascii=False)

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            stats = {}

            # إحصائيات الطلاب
            try:
                cursor.execute("SELECT COUNT(*) FROM اللوائح")
                stats['students_count'] = cursor.fetchone()[0]
            except:
                stats['students_count'] = 0

            # إحصائيات الأساتذة
            try:
                cursor.execute("SELECT COUNT(*) FROM الأساتذة")
                stats['teachers_count'] = cursor.fetchone()[0]
            except:
                stats['teachers_count'] = 0

            # إحصائيات الأقسام
            try:
                cursor.execute("SELECT COUNT(DISTINCT القسم) FROM اللوائح")
                stats['sections_count'] = cursor.fetchone()[0]
            except:
                stats['sections_count'] = 0

            # إحصائيات الرموز السرية
            try:
                cursor.execute("SELECT COUNT(*) FROM الرمز_السري")
                stats['secret_codes_count'] = cursor.fetchone()[0]
            except:
                stats['secret_codes_count'] = 0

            conn.close()
            return json.dumps(stats, ensure_ascii=False)

        except Exception as e:
            return json.dumps({"error": str(e)}, ensure_ascii=False)

class ModernSPASystem(QMainWindow):
    """النظام الرئيسي الحديث - نظام الصفحات الموحد (ملف واحد شامل)"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎓 النظام التعليمي الشامل - ملف واحد شامل")
        self.setGeometry(100, 100, 1200, 800)

        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك النظام
        self.system_engine = SystemEngine()
        self.system_engine.main_window = self  # ربط النافذة الرئيسية

        # متغير لحفظ نافذة الاستيراد
        self.import_window = None

        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء عرض الويب الموحد
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)

        # تحميل واجهة HTML الموحدة
        html_content = self.get_complete_html()
        self.web_view.setHtml(html_content)

    def setup_web_channel(self):
        """إعداد قناة التواصل مع JavaScript"""
        self.channel = QWebChannel()
        self.channel.registerObject("systemEngine", self.system_engine)
        self.web_view.page().setWebChannel(self.channel)

        # انتظار تحميل الصفحة قبل إعداد القناة
        self.web_view.loadFinished.connect(self.on_page_loaded)

    def on_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة"""
        # إعادة تسجيل الكائن للتأكد من الربط
        self.channel.registerObject("systemEngine", self.system_engine)

    def open_import_window(self):
        """فتح نافذة الاستيراد مدمجة داخل النافذة الرئيسية"""
        if SUB1_WINDOW_AVAILABLE:
            # إنشاء نافذة الاستيراد إذا لم تكن موجودة
            if self.import_window is None:
                self.import_window = ModernSub1Window()

                # ربط إشارة العودة للرئيسية
                self.import_window.returnToMain.connect(self.close_import_window)

                # إعداد النافذة كنافذة مدمجة
                self.import_window.setParent(self)
                self.import_window.setWindowFlags(Qt.Widget)

                # الحصول على layout الرئيسي
                main_layout = self.centralWidget().layout()
                if main_layout:
                    # إضافة نافذة الاستيراد
                    main_layout.addWidget(self.import_window)

            # إخفاء المحتوى الرئيسي وإظهار نافذة الاستيراد
            self.web_view.hide()
            self.import_window.show()
            self.import_window.raise_()

    def close_import_window(self):
        """إغلاق نافذة الاستيراد والعودة للنافذة الرئيسية"""
        if self.import_window and self.import_window.isVisible():
            self.import_window.hide()
        # إظهار المحتوى الرئيسي مرة أخرى
        self.web_view.show()

    def closeEvent(self, event):
        """إغلاق النوافذ الفرعية عند إغلاق النافذة الرئيسية"""
        if self.import_window and self.import_window.isVisible():
            self.import_window.close()
        event.accept()

    def get_complete_html(self):
        """HTML كامل مع CSS و JavaScript - كل شيء في ملف واحد"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام التعليمي الشامل - ملف واحد شامل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            animation: none !important;
            transition: all 0.3s ease !important;
        }

        body {
            font-family: 'Calibri', 'Tahoma', 'Arial', sans-serif;
            font-size: 14px;
            font-weight: bold;
            background: #f8f9fa;
            direction: rtl;
            overflow-x: hidden;
        }

        /* شريط التنقل العلوي */
        .navbar {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            color: white;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .navbar-brand {
            font-size: 24px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .navbar-nav {
            display: flex;
            gap: 5px;
            list-style: none;
        }

        .nav-item {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .nav-link {
            display: block;
            padding: 10px 15px;
            color: white;
            text-decoration: none;
            font-size: 13px;
            font-weight: bold;
            cursor: pointer;
        }

        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.2);
            color: white;
        }

        .nav-link.active {
            background: rgba(255,255,255,0.3);
        }

        /* شريط الحالة */
        .status-bar {
            background: #e9ecef;
            padding: 10px 20px;
            font-size: 12px;
            color: #6c757d;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
        }

        /* منطقة المحتوى */
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
            min-height: calc(100vh - 120px);
        }

        /* الصفحات */
        .page {
            display: none;
            opacity: 0;
            transform: translateY(20px);
        }

        .page.active {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }

        /* صفحة الرئيسية */
        .welcome-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }

        .welcome-section h1 {
            color: #2c3e50;
            font-size: 32px;
            margin-bottom: 15px;
        }

        .welcome-section p {
            color: #7f8c8d;
            font-size: 16px;
            line-height: 1.6;
        }

        /* إحصائيات */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #9b59b6;
        }

        .stat-value {
            font-size: 36px;
            font-weight: bold;
            color: #9b59b6;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }

        /* بطاقات الوحدات */
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .module-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            cursor: pointer;
            border: 2px solid transparent;
        }

        .module-card:hover {
            border-color: #9b59b6;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .module-icon {
            font-size: 50px;
            margin-bottom: 15px;
            display: block;
        }

        .module-title {
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .module-description {
            color: #6c757d;
            font-size: 13px;
            line-height: 1.5;
        }

        /* صفحات الوحدات */
        .module-page {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .page-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f8f9fa;
        }

        .page-header h2 {
            color: #2c3e50;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .page-header p {
            color: #6c757d;
            font-size: 14px;
        }

        /* أزرار */
        .btn {
            background: #9b59b6;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 25px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background: #8e44ad;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        /* لوحة التحميل */
        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }

        .loading-spinner {
            font-size: 30px;
            margin-bottom: 15px;
        }

        /* تجاوب */
        @media (max-width: 768px) {
            .navbar-container {
                flex-direction: column;
                gap: 15px;
            }

            .navbar-nav {
                flex-wrap: wrap;
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .modules-grid {
                grid-template-columns: 1fr;
            }

            .main-content {
                padding: 20px 15px;
            }
        }

        /* تأثيرات خاصة */
        .highlight {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }

        .info-box {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .info-box h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .info-box p {
            color: #6c757d;
            font-size: 13px;
            line-height: 1.5;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar">
        <div class="navbar-container">
            <div class="navbar-brand">
                🎓 النظام التعليمي الشامل
            </div>
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link active" onclick="navigateToPage('home')" data-page="home">🏠 الرئيسية</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" onclick="navigateToPage('import')" data-page="import">📥 الاستيراد</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" onclick="navigateToPage('settings')" data-page="settings">⚙️ الإعدادات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" onclick="navigateToPage('institution')" data-page="institution">🏢 المؤسسة</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" onclick="navigateToPage('statistics')" data-page="statistics">📊 الإحصائيات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" onclick="navigateToPage('documents')" data-page="documents">📝 المستندات</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" onclick="navigateToPage('help')" data-page="help">❓ المساعدة</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- شريط الحالة -->
    <div class="status-bar">
        <div class="status-indicator">
            <div class="status-dot"></div>
            <span>النظام متصل ويعمل بشكل طبيعي</span>
        </div>
        <span id="current-time">جاري التحميل...</span>
    </div>

    <!-- منطقة المحتوى الرئيسية -->
    <main class="main-content">
        <!-- صفحة الرئيسية -->
        <div id="home-page" class="page active">
            <div class="welcome-section">
                <h1>مرحباً بك في النظام التعليمي الشامل</h1>
                <p>منظومة متكاملة لإدارة البيانات التعليمية والطلابية - ملف واحد شامل</p>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="studentsCount">0</div>
                    <div class="stat-label">الطلاب المسجلين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="teachersCount">0</div>
                    <div class="stat-label">أعضاء هيئة التدريس</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="sectionsCount">0</div>
                    <div class="stat-label">الأقسام الأكاديمية</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="secretCodesCount">0</div>
                    <div class="stat-label">الرموز السرية</div>
                </div>
            </div>

            <!-- بطاقات الوحدات -->
            <div class="modules-grid">
                <div class="module-card" onclick="loadImportSystem()">
                    <span class="module-icon">📥</span>
                    <div class="module-title">نظام الاستيراد</div>
                    <div class="module-description">استيراد البيانات من ملفات Excel ومنظومة مسار</div>
                </div>
                <div class="module-card" onclick="navigateToPage('settings')">
                    <span class="module-icon">⚙️</span>
                    <div class="module-title">إعدادات النظام</div>
                    <div class="module-description">النسخ الاحتياطي والصيانة وإعدادات النظام</div>
                </div>
                <div class="module-card" onclick="navigateToPage('institution')">
                    <span class="module-icon">🏢</span>
                    <div class="module-title">بيانات المؤسسة</div>
                    <div class="module-description">إدارة معلومات المؤسسة والأقسام الأكاديمية</div>
                </div>
                <div class="module-card" onclick="navigateToPage('statistics')">
                    <span class="module-icon">📊</span>
                    <div class="module-title">الإحصائيات والتقارير</div>
                    <div class="module-description">تقارير شاملة وإحصائيات تفصيلية</div>
                </div>
                <div class="module-card" onclick="navigateToPage('documents')">
                    <span class="module-icon">📝</span>
                    <div class="module-title">إدارة المستندات</div>
                    <div class="module-description">تنظيم وإدارة الأوراق والملاحظات</div>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="highlight">
                <h3>🎉 ملف واحد شامل!</h3>
                <p>لا حاجة لملفات أخرى - كل شيء في مكان واحد</p>
            </div>
        </div>

        <!-- صفحة نظام الاستيراد -->
        <div id="import-page" class="page">
            <div class="module-page">
                <div class="page-header">
                    <h2>📥 نظام الاستيراد</h2>
                    <p>استيراد البيانات من ملفات Excel ومنظومة مسار</p>
                </div>
                <div class="info-box">
                    <h4>🔧 الوظائف المتاحة:</h4>
                    <p>• استيراد اللوائح من منظومة مسار</p>
                    <p>• استيراد الرموز السرية دفعة واحدة</p>
                    <p>• استيراد أسماء الأساتذة والمواد</p>
                    <p>• معالجة ملفات Excel المختلفة</p>
                </div>
                <div class="loading">
                    <div class="loading-spinner">📥</div>
                    <p>نظام الاستيراد جاهز للاستخدام</p>
                    <button class="btn" onclick="loadImportSystem()">تشغيل نظام الاستيراد</button>
                </div>
            </div>
        </div>

        <!-- صفحة الإعدادات -->
        <div id="settings-page" class="page">
            <div class="module-page">
                <div class="page-header">
                    <h2>⚙️ إعدادات النظام</h2>
                    <p>النسخ الاحتياطي والصيانة وإعدادات النظام</p>
                </div>
                <div class="info-box">
                    <h4>🛠️ الوظائف المتاحة:</h4>
                    <p>• النسخ الاحتياطي واستعادة البيانات</p>
                    <p>• تنظيف وصيانة قاعدة البيانات</p>
                    <p>• تهيئة موسم دراسي جديد</p>
                    <p>• إعدادات النظام والتخصيص</p>
                </div>
                <div class="loading">
                    <div class="loading-spinner">⚙️</div>
                    <p>نظام الإعدادات جاهز للاستخدام</p>
                    <button class="btn" onclick="loadSettingsSystem()">تشغيل نظام الإعدادات</button>
                </div>
            </div>
        </div>

        <!-- صفحة بيانات المؤسسة -->
        <div id="institution-page" class="page">
            <div class="module-page">
                <div class="page-header">
                    <h2>🏢 بيانات المؤسسة</h2>
                    <p>إدارة معلومات المؤسسة والأقسام الأكاديمية</p>
                </div>
                <div style="text-align: center; padding: 50px;">
                    <span style="font-size: 80px;">🚧</span>
                    <h3 style="color: #6c757d; margin: 20px 0;">قيد التطوير</h3>
                    <p style="color: #6c757d;">سيتم إضافة هذه الوحدة في التحديثات القادمة</p>
                    <button class="btn btn-secondary" onclick="navigateToPage('home')">العودة للرئيسية</button>
                </div>
            </div>
        </div>

        <!-- صفحة الإحصائيات -->
        <div id="statistics-page" class="page">
            <div class="module-page">
                <div class="page-header">
                    <h2>📊 الإحصائيات والتقارير</h2>
                    <p>تقارير شاملة وإحصائيات تفصيلية</p>
                </div>
                <div style="text-align: center; padding: 50px;">
                    <span style="font-size: 80px;">🚧</span>
                    <h3 style="color: #6c757d; margin: 20px 0;">قيد التطوير</h3>
                    <p style="color: #6c757d;">سيتم إضافة هذه الوحدة في التحديثات القادمة</p>
                    <button class="btn btn-secondary" onclick="navigateToPage('home')">العودة للرئيسية</button>
                </div>
            </div>
        </div>

        <!-- صفحة المستندات -->
        <div id="documents-page" class="page">
            <div class="module-page">
                <div class="page-header">
                    <h2>📝 إدارة المستندات</h2>
                    <p>تنظيم وإدارة الأوراق والملاحظات</p>
                </div>
                <div style="text-align: center; padding: 50px;">
                    <span style="font-size: 80px;">🚧</span>
                    <h3 style="color: #6c757d; margin: 20px 0;">قيد التطوير</h3>
                    <p style="color: #6c757d;">سيتم إضافة هذه الوحدة في التحديثات القادمة</p>
                    <button class="btn btn-secondary" onclick="navigateToPage('home')">العودة للرئيسية</button>
                </div>
            </div>
        </div>

        <!-- صفحة المساعدة -->
        <div id="help-page" class="page">
            <div class="module-page">
                <div class="page-header">
                    <h2>❓ المساعدة والدعم</h2>
                    <p>دليل الاستخدام والمساعدة الفنية</p>
                </div>
                <div class="info-box">
                    <h4>🎯 كيفية الاستخدام:</h4>
                    <p>• استخدم شريط التنقل العلوي للانتقال بين الصفحات</p>
                    <p>• انقر على بطاقات الوحدات في الصفحة الرئيسية</p>
                    <p>• تابع الإحصائيات المحدثة في الصفحة الرئيسية</p>
                    <p>• استخدم أزرار التشغيل لتحميل الوحدات المتاحة</p>
                </div>

                <div class="info-box">
                    <h4>🔧 الوحدات المتاحة:</h4>
                    <p>• <strong>نظام الاستيراد:</strong> استيراد البيانات من ملفات Excel</p>
                    <p>• <strong>إعدادات النظام:</strong> النسخ الاحتياطي والصيانة</p>
                    <p>• <strong>بيانات المؤسسة:</strong> قيد التطوير</p>
                    <p>• <strong>الإحصائيات:</strong> قيد التطوير</p>
                    <p>• <strong>المستندات:</strong> قيد التطوير</p>
                </div>

                <div class="highlight">
                    <h4>✨ مميزات هذه النسخة:</h4>
                    <p>ملف واحد شامل - تنقل سلس - أداء محسن - بدون تموجات</p>
                    <button class="btn" onclick="showSystemHelp()">عرض المساعدة التفصيلية</button>
                </div>
            </div>
        </div>
    </main>

    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <script>
        let systemEngine = null;
        let isChannelReady = false;
        let currentPage = 'home';

        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    systemEngine = channel.objects.systemEngine;
                    isChannelReady = true;

                    console.log('🚀 QWebChannel initialized successfully');

                    // ربط الإشارات
                    if (systemEngine) {
                        systemEngine.logUpdated.connect(addLogEntry);
                        systemEngine.systemStatusUpdated.connect(updateSystemStatus);
                        systemEngine.pageChanged.connect(onPageChanged);
                        systemEngine.serverStatusUpdated.connect(updateServerStatus);

                        // تحميل حالة النظام والإحصائيات
                        loadSystemStatus();
                        loadDatabaseStatistics();

                        // فحص حالة الخادم
                        systemEngine.check_server_status();

                        console.log('✅ تم تهيئة النظام بنجاح');
                    }
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // التنقل بين الصفحات
        function navigateToPage(pageName) {
            // إخفاء جميع الصفحات
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => {
                page.classList.remove('active');
            });

            // إظهار الصفحة المطلوبة
            const targetPage = document.getElementById(pageName + '-page');
            if (targetPage) {
                targetPage.classList.add('active');
                currentPage = pageName;

                // تحديث شريط التنقل
                updateNavigation(pageName);

                // إشعار Python بالتغيير
                if (systemEngine) {
                    systemEngine.navigateToPage(pageName);
                }

                console.log('📄 تم الانتقال إلى صفحة:', pageName);
            }
        }

        // تحديث شريط التنقل
        function updateNavigation(activePage) {
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('data-page') === activePage) {
                    link.classList.add('active');
                }
            });
        }

        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // تحميل حالة النظام
        function loadSystemStatus() {
            if (systemEngine) {
                try {
                    const systemStatus = JSON.parse(systemEngine.getSystemStatus());
                    console.log('System Status:', systemStatus);
                } catch (error) {
                    console.error('خطأ في تحميل حالة النظام:', error);
                }
            }
        }

        // تحميل إحصائيات قاعدة البيانات
        function loadDatabaseStatistics() {
            if (systemEngine) {
                try {
                    const stats = JSON.parse(systemEngine.getDatabaseStatistics());

                    document.getElementById('studentsCount').textContent = stats.students_count || 0;
                    document.getElementById('teachersCount').textContent = stats.teachers_count || 0;
                    document.getElementById('sectionsCount').textContent = stats.sections_count || 0;
                    document.getElementById('secretCodesCount').textContent = stats.secret_codes_count || 0;

                } catch (error) {
                    console.error('خطأ في تحميل الإحصائيات:', error);
                }
            }
        }

        // تحديث حالة الخادم
        function updateServerStatus(isConnected, serverInfo) {
            console.log('Server status updated:', isConnected, serverInfo);
        }

        // إضافة إدخال إلى السجل
        function addLogEntry(message, status, timestamp) {
            console.log(`[${timestamp}] ${status.toUpperCase()}: ${message}`);
        }

        // تحديث حالة النظام
        function updateSystemStatus(statusJson) {
            console.log('System status updated:', statusJson);
        }

        // استجابة لتغيير الصفحة من Python
        function onPageChanged(pageName) {
            console.log('Page changed from Python:', pageName);
        }

        // تحميل نظام الاستيراد
        function loadImportSystem() {
            if (systemEngine) {
                systemEngine.openImportSystem();
            }
        }

        // إغلاق نظام الاستيراد
        function closeImportSystem() {
            if (systemEngine) {
                systemEngine.closeImportSystem();
            }
        }

        // تحميل نظام الإعدادات
        function loadSettingsSystem() {
            if (systemEngine) {
                systemEngine.openSettingsSystem();
                alert('🔄 تم إرسال طلب فتح نظام الإعدادات\\n\\nتحقق من وحدة التحكم للمزيد من المعلومات.');
            } else {
                alert('❌ النظام غير جاهز بعد');
            }
        }

        // عرض مساعدة النظام
        function showSystemHelp() {
            if (systemEngine) {
                systemEngine.showSystemHelp();
                alert('📖 تم عرض المساعدة التفصيلية\\n\\nتحقق من وحدة التحكم لقراءة المساعدة الكاملة.');
            } else {
                alert('❌ النظام غير جاهز بعد');
            }
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌟 تحميل النظام التعليمي الشامل - ملف واحد شامل...');

            // تحديث الوقت
            updateTime();
            setInterval(updateTime, 1000);

            // تهيئة قناة التواصل
            initializeChannel();

            // تحديث الإحصائيات كل 30 ثانية
            setInterval(() => {
                if (systemEngine) {
                    loadDatabaseStatistics();
                }
            }, 30000);

            // تحديد الصفحة الافتراضية
            navigateToPage('home');

            // إضافة مستمع لمفتاح Escape لإغلاق النوافذ المدمجة
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    closeImportSystem();
                }
            });
        });

        console.log('🚀 تم تحميل النظام الشامل بنجاح!');
    </script>
</body>
</html>
        """

def main():
    """تشغيل النظام الرئيسي - ملف واحد شامل"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("النظام التعليمي الشامل - ملف واحد شامل")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Modern Education Systems")

    # إنشاء النافذة الرئيسية
    window = ModernSPASystem()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == '__main__':
    print("🌐 بدء تشغيل النظام التعليمي الشامل - ملف واحد شامل...")
    print("=" * 60)
    print("📋 الميزات:")
    print("   🔹 ملف واحد فقط - لا حاجة لملفات أخرى")
    print("   🔹 تنقل سلس مثل صفحات الإنترنت")
    print("   🔹 شريط تنقل علوي ثابت")
    print("   🔹 صفحات محملة ديناميكياً")
    print("   🔹 بدون تموجات أو تأثيرات مزعجة")
    print("   🔹 أداء محسن ومستقر")
    print("=" * 60)
    print("🎯 كيفية الاستخدام:")
    print("   • استخدم شريط التنقل العلوي للانتقال بين الصفحات")
    print("   • انقر على بطاقات الوحدات في الصفحة الرئيسية")
    print("   • تابع الإحصائيات المحدثة في الصفحة الرئيسية")
    print("=" * 60)
    print("🚀 جاري تشغيل النظام...")

    try:
        main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5 PyQtWebEngine")
