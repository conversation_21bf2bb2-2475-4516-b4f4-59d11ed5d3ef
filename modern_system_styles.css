/*
ملف الأنماط الموحد للنظام التعليمي الحديث
يحتوي على جميع الأنماط المشتركة بين الوحدات
*/

/* إعدادات أساسية */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Aria<PERSON>', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 100vh;
    direction: rtl;
}

/* الحاوي الرئيسي */
.container {
    max-width: 95%;
    margin: 20px auto;
    display: grid;
    gap: 20px;
    height: calc(100vh - 40px);
}

/* شريط العنوان الموحد */
.header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.header h1 {
    color: #667eea;
    font-size: 24px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.header p {
    color: #666;
    font-size: 14px;
    opacity: 0.9;
}

/* اللوحات الموحدة */
.panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.panel-title {
    color: #667eea;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* الأزرار الموحدة */
.btn {
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 13px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #2980b9, #1f618d);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #27ae60, #229954);
    transform: translateY(-2px);
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e67e22, #d35400);
    transform: translateY(-2px);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #d62c1a, #a93226);
    transform: translateY(-2px);
}

/* البطاقات الموحدة */
.card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    transition: all 0.3s ease;
}

.card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.card-title {
    font-size: 16px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 8px;
}

.card-description {
    font-size: 13px;
    color: #6c757d;
    margin-bottom: 15px;
    line-height: 1.4;
}

/* الشبكات الموحدة */
.grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.grid-4 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.grid-auto {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

/* السجلات الموحدة */
.log-container {
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    padding: 15px;
    height: 300px;
    overflow-y: auto;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.6;
}

.log-entry {
    margin-bottom: 8px;
    padding: 5px 10px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.log-entry:hover {
    background: rgba(255, 255, 255, 0.5);
    transform: translateX(5px);
}

.log-entry.info {
    background: rgba(52, 152, 219, 0.1);
    border-left: 4px solid #3498db;
}

.log-entry.success {
    background: rgba(46, 204, 113, 0.1);
    border-left: 4px solid #2ecc71;
}

.log-entry.error {
    background: rgba(231, 76, 60, 0.1);
    border-left: 4px solid #e74c3c;
}

.log-entry.warning {
    background: rgba(243, 156, 18, 0.1);
    border-left: 4px solid #f39c12;
}

.log-entry.progress {
    background: rgba(155, 89, 182, 0.1);
    border-left: 4px solid #9b59b6;
}

.log-timestamp {
    color: #7f8c8d;
    margin-left: auto;
    font-size: 11px;
}

/* شريط التقدم الموحد */
.progress-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.98);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    min-width: 400px;
    z-index: 1000;
    display: none;
}

.progress-bar {
    width: 100%;
    height: 25px;
    background: #ecf0f1;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 15px;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.4s ease;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    text-align: center;
    color: #2c3e50;
    font-weight: bold;
    font-size: 14px;
}

/* الإحصائيات الموحدة */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.stat-item {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.stat-label {
    font-size: 12px;
    opacity: 0.9;
    font-weight: 600;
}

/* معلومات النظام */
.system-info {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 13px;
}

.info-label {
    font-weight: bold;
    color: #2c3e50;
}

.info-value {
    color: #34495e;
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #667eea;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تأثيرات الرسوم المتحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.float {
    animation: float 6s ease-in-out infinite;
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .grid-2, .grid-3, .grid-4 {
        grid-template-columns: 1fr;
    }
    
    .container {
        margin: 10px;
        height: calc(100vh - 20px);
    }
    
    .header h1 {
        font-size: 20px;
    }
    
    .panel {
        padding: 15px;
    }
    
    .progress-container {
        min-width: 300px;
        padding: 20px;
    }
}

/* ألوان إضافية للتنويع */
.color-blue { background: linear-gradient(135deg, #3498db, #2980b9); }
.color-green { background: linear-gradient(135deg, #2ecc71, #27ae60); }
.color-orange { background: linear-gradient(135deg, #f39c12, #e67e22); }
.color-red { background: linear-gradient(135deg, #e74c3c, #c0392b); }
.color-purple { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
.color-teal { background: linear-gradient(135deg, #1abc9c, #16a085); }
