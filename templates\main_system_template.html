<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام التعليمي الشامل - الإصدار المحسن</title>
    
    <!-- تحميل الأنماط -->
    <link rel="stylesheet" href="styles/main_system.css">
    
    <!-- تحسينات الأداء -->
    <link rel="preconnect" href="qrc:///qtwebchannel/">
    <meta name="theme-color" content="#9b59b6">
    
    <!-- إعدادات إضافية للأداء -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
</head>
<body>
    <div class="main-container">
        <!-- شريط العنوان -->
        <div class="header">
            <h1>🎓 النظام التعليمي الشامل</h1>
            <p>منظومة متكاملة لإدارة البيانات التعليمية والطلابية - الإصدار المحسن</p>
        </div>

        <!-- شريط الحالة -->
        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>النظام متصل ويعمل بشكل طبيعي</span>
            </div>
            <span id="current-time">جاري التحميل...</span>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="dashboard-content">
            <!-- إحصائيات سريعة -->
            <div class="stats-container" id="statsContainer">
                <div class="stat-item">
                    <div class="stat-value" id="studentsCount">0</div>
                    <div class="stat-label">الطلاب</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="teachersCount">0</div>
                    <div class="stat-label">الأساتذة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="sectionsCount">0</div>
                    <div class="stat-label">الأقسام</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="secretCodesCount">0</div>
                    <div class="stat-label">الرموز السرية</div>
                </div>
            </div>

            <!-- وصول سريع -->
            <div class="quick-access">
                <h3>🚀 وصول سريع</h3>
                <div class="quick-links">
                    <div class="quick-link" onclick="openImportSystem()">
                        📥 نظام الاستيراد
                    </div>
                    <div class="quick-link" onclick="openSettingsSystem()">
                        ⚙️ إعدادات النظام
                    </div>
                    <div class="quick-link" onclick="openInstitutionSystem()">
                        🏢 بيانات المؤسسة
                    </div>
                    <div class="quick-link" onclick="openStatisticsSystem()">
                        📊 الإحصائيات
                    </div>
                    <div class="quick-link" onclick="openDocumentsSystem()">
                        📝 المستندات
                    </div>
                    <div class="quick-link" onclick="openPrinterSettings()">
                        🖨️ الطابعة
                    </div>
                    <div class="quick-link" onclick="manageServer()">
                        🔗 إدارة الخادم
                    </div>
                    <div class="quick-link" onclick="showHelp()">
                        ❓ المساعدة
                    </div>
                </div>
            </div>

            <!-- شبكة الوحدات الرئيسية -->
            <div class="modules-grid">
                <!-- وحدة استيراد البيانات -->
                <div class="module-card" onclick="openImportSystem()">
                    <span class="module-icon">📥</span>
                    <div class="module-title">نظام الاستيراد</div>
                    <div class="module-description">
                        استيراد اللوائح من منظومة مسار، الرموز السرية، وأسماء الأساتذة والمواد المدرسة
                        <br><strong>الميزات:</strong> معالجة متقدمة، تحكم في الخط، تقارير مفصلة
                    </div>
                    <button class="module-button" onclick="openImportSystem()">
                        🚀 فتح نظام الاستيراد
                    </button>
                </div>

                <!-- وحدة إعدادات البرنامج -->
                <div class="module-card" onclick="openSettingsSystem()">
                    <span class="module-icon">⚙️</span>
                    <div class="module-title">إعدادات النظام</div>
                    <div class="module-description">
                        إدارة إعدادات النظام، النسخ الاحتياطي، الاستعادة، وصيانة قاعدة البيانات
                        <br><strong>الميزات:</strong> نسخ احتياطي، تهيئة موسم جديد، حذف البيانات
                    </div>
                    <button class="module-button secondary" onclick="openSettingsSystem()">
                        ⚙️ فتح إعدادات النظام
                    </button>
                </div>

                <!-- وحدة بيانات المؤسسة -->
                <div class="module-card" onclick="openInstitutionSystem()">
                    <span class="module-icon">🏢</span>
                    <div class="module-title">بيانات المؤسسة</div>
                    <div class="module-description">
                        إدارة بيانات المؤسسة التعليمية، المعلومات الأساسية، والشعار الرسمي
                        <br><strong>الميزات:</strong> تحديد اسم المؤسسة، العنوان، الشعار، معلومات الاتصال
                    </div>
                    <button class="module-button success" onclick="openInstitutionSystem()">
                        🏢 إدارة بيانات المؤسسة
                    </button>
                </div>

                <!-- وحدة الإحصائيات -->
                <div class="module-card" onclick="openStatisticsSystem()">
                    <span class="module-icon">📊</span>
                    <div class="module-title">الإحصائيات</div>
                    <div class="module-description">
                        تقارير شاملة وإحصائيات مفصلة للطلاب، الدرجات، والأداء الأكاديمي
                        <br><strong>الميزات:</strong> رسوم بيانية، تقارير دورية، تحليل البيانات
                    </div>
                    <button class="module-button warning" onclick="openStatisticsSystem()">
                        📊 عرض الإحصائيات
                    </button>
                </div>

                <!-- وحدة عناوين الأوراق والملاحظات -->
                <div class="module-card" onclick="openDocumentsSystem()">
                    <span class="module-icon">📝</span>
                    <div class="module-title">عناوين الأوراق والملاحظات</div>
                    <div class="module-description">
                        إدارة عناوين المستندات، الأوراق الرسمية، والملاحظات الإدارية
                        <br><strong>الميزات:</strong> قوالب جاهزة، تخصيص العناوين، حفظ الملاحظات
                    </div>
                    <button class="module-button" onclick="openDocumentsSystem()">
                        📝 إدارة المستندات
                    </button>
                </div>

                <!-- وحدة إعدادات الطابعة -->
                <div class="module-card" onclick="openPrinterSettings()">
                    <span class="module-icon">🖨️</span>
                    <div class="module-title">إعدادات الطابعة</div>
                    <div class="module-description">
                        تكوين إعدادات الطباعة، اختيار الطابعة الافتراضية، وضبط جودة الطباعة
                        <br><strong>الميزات:</strong> اختيار الطابعة، حجم الورق، جودة الطباعة
                    </div>
                    <button class="module-button secondary" onclick="openPrinterSettings()">
                        🖨️ إعدادات الطباعة
                    </button>
                </div>

                <!-- وحدة الاتصال بالخادم وتشغيله -->
                <div class="module-card">
                    <span class="module-icon">🔗</span>
                    <div class="module-title">الاتصال بالخادم وتشغيله</div>
                    <div class="module-description">
                        مراقبة حالة الخادم، إدارة الاتصال، وتشغيل الخدمات الأساسية
                        <br><strong>الحالة:</strong> <span class="server-status">جاري التحقق...</span>
                    </div>
                    <button class="module-button warning" onclick="manageServer()">
                        🔗 إدارة الخادم
                    </button>
                </div>

                <!-- وحدة المساعدة والدعم -->
                <div class="module-card">
                    <span class="module-icon">❓</span>
                    <div class="module-title">المساعدة والدعم</div>
                    <div class="module-description">
                        أدلة الاستخدام، الأسئلة الشائعة، ومعلومات التقنية
                        <br><strong>متوفر:</strong> أدلة شاملة، فيديوهات تعليمية، دعم فني
                    </div>
                    <button class="module-button success" onclick="showHelp()">
                        📖 عرض المساعدة
                    </button>
                </div>
            </div>
        </div>

        <!-- تذييل الصفحة -->
        <div class="footer">
            <p>© 2025 النظام التعليمي الشامل - تم التطوير بواسطة فريق التقنية التعليمية</p>
            <p>الإصدار المحسن 2.0 | آخر تحديث: يونيو 2025 | تقنية Python + HTML المتقدمة</p>
        </div>
    </div>

    <!-- تحميل المكتبات والسكريبت -->
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <script src="scripts/main_system.js"></script>
    
    <!-- سكريبت إضافي للتحسينات -->
    <script>
        // تحسينات إضافية للأداء
        document.addEventListener('DOMContentLoaded', function() {
            // إخفاء شاشة التحميل إذا كانت موجودة
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                setTimeout(() => {
                    loadingScreen.style.opacity = '0';
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 300);
                }, 1000);
            }
            
            // تحسين الصور إذا كانت موجودة
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                img.loading = 'lazy';
            });
        });
    </script>
</body>
</html>
