/*
ملف JavaScript الرئيسي للنظام التعليمي الشامل
تم فصله لتحسين الأداء وسهولة الصيانة
*/

let systemEngine = null;
let isChannelReady = false;

// إعداد قناة التواصل مع Python
function initializeChannel() {
    if (typeof qt !== 'undefined' && qt.webChannelTransport) {
        new QWebChannel(qt.webChannelTransport, function(channel) {
            systemEngine = channel.objects.systemEngine;
            isChannelReady = true;
            
            console.log('🚀 QWebChannel initialized successfully');
            
            // ربط الإشارات
            if (systemEngine) {
                systemEngine.logUpdated.connect(addLogEntry);
                systemEngine.systemStatusUpdated.connect(updateSystemStatus);
                systemEngine.moduleStatusUpdated.connect(updateModuleStatus);
                systemEngine.serverStatusUpdated.connect(updateServerStatus);
                
                // تحميل حالة النظام والإحصائيات
                loadSystemStatus();
                loadDatabaseStatistics();
                
                // فحص حالة الخادم
                systemEngine.check_server_status();
                
                console.log('✅ تم تهيئة النظام بنجاح');
            }
        });
    } else {
        console.log('⚠️ QWebChannel not available, retrying...');
        setTimeout(initializeChannel, 100);
    }
}

// تحديث الوقت
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric', 
        hour: '2-digit', 
        minute: '2-digit',
        second: '2-digit'
    });
    
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

// تحميل حالة النظام
function loadSystemStatus() {
    if (systemEngine) {
        try {
            const systemStatus = JSON.parse(systemEngine.getSystemStatus());
            console.log('System Status:', systemStatus);
            
            // يمكن إضافة معالجة إضافية لحالة النظام هنا
        } catch (error) {
            console.error('خطأ في تحميل حالة النظام:', error);
        }
    }
}

// تحميل إحصائيات قاعدة البيانات
function loadDatabaseStatistics() {
    if (systemEngine) {
        try {
            const stats = JSON.parse(systemEngine.getDatabaseStatistics());
            
            updateStatElement('studentsCount', stats.students_count || 0);
            updateStatElement('teachersCount', stats.teachers_count || 0);
            updateStatElement('sectionsCount', stats.sections_count || 0);
            updateStatElement('secretCodesCount', stats.secret_codes_count || 0);
            
        } catch (error) {
            console.error('خطأ في تحميل الإحصائيات:', error);
        }
    }
}

// تحديث عنصر إحصائي
function updateStatElement(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        // تحديث مباشر بدون تأثيرات
        updateNumberDirectly(element, value);
    }
}

// تحديث مباشر للأرقام بدون تأثيرات
function updateNumberDirectly(element, value) {
    element.textContent = value;
}

// تحديث حالة الخادم
function updateServerStatus(isConnected, serverInfo) {
    console.log('Server status updated:', isConnected, serverInfo);
    
    // يمكن إضافة تحديث واجهة المستخدم هنا
    const statusElements = document.querySelectorAll('.server-status');
    statusElements.forEach(element => {
        if (isConnected) {
            element.textContent = 'متصل ويعمل ✅';
            element.style.color = '#28a745';
        } else {
            element.textContent = 'غير متصل ❌';
            element.style.color = '#dc3545';
        }
    });
}

// إضافة إدخال إلى السجل (للاستخدام المستقبلي)
function addLogEntry(message, status, timestamp) {
    console.log(`[${timestamp}] ${status.toUpperCase()}: ${message}`);
}

// تحديث حالة النظام
function updateSystemStatus(statusJson) {
    console.log('System status updated:', statusJson);
}

// تحديث حالة الوحدة
function updateModuleStatus(moduleName, status) {
    console.log(`Module ${moduleName} status: ${status}`);
}

// وظائف فتح الوحدات
function openImportSystem() {
    if (systemEngine) {
        console.log('🔄 فتح نظام الاستيراد...');
        systemEngine.openImportSystem();
    } else {
        showAlert('❌ النظام غير جاهز بعد');
    }
}

function openSettingsSystem() {
    if (systemEngine) {
        console.log('🔄 فتح نظام الإعدادات...');
        systemEngine.openSettingsSystem();
    } else {
        showAlert('❌ النظام غير جاهز بعد');
    }
}

function openInstitutionSystem() {
    if (systemEngine) {
        console.log('🔄 فتح نظام بيانات المؤسسة...');
        systemEngine.openInstitutionSystem();
    } else {
        showAlert('❌ النظام غير جاهز بعد');
    }
}

function openStatisticsSystem() {
    if (systemEngine) {
        console.log('🔄 فتح نظام الإحصائيات...');
        systemEngine.openStatisticsSystem();
    } else {
        showAlert('❌ النظام غير جاهز بعد');
    }
}

function openDocumentsSystem() {
    if (systemEngine) {
        console.log('🔄 فتح نظام المستندات...');
        systemEngine.openDocumentsSystem();
    } else {
        showAlert('❌ النظام غير جاهز بعد');
    }
}

function openPrinterSettings() {
    if (systemEngine) {
        console.log('🔄 فتح إعدادات الطابعة...');
        systemEngine.openPrinterSettings();
    } else {
        showAlert('❌ النظام غير جاهز بعد');
    }
}

function manageServer() {
    if (systemEngine) {
        console.log('🔄 إدارة الخادم...');
        systemEngine.manageServer();
    } else {
        showAlert('❌ النظام غير جاهز بعد');
    }
}

function showHelp() {
    if (systemEngine) {
        console.log('📖 عرض المساعدة...');
        systemEngine.showSystemHelp();
    } else {
        showAlert('❌ النظام غير جاهز بعد');
    }
}

// تأثير بصري مبسط للنقر على الأزرار
function animateButtonClick(button) {
    if (!button) return;

    // تأثير بسيط بدون تموجات
    button.style.opacity = '0.8';
    setTimeout(() => {
        button.style.opacity = '';
    }, 100);
}

// عرض تنبيه مخصص
function showAlert(message) {
    // يمكن استبدال هذا بنظام تنبيهات أكثر تقدماً
    alert(message);
}

// تحديث مبسط للإحصائيات
function scheduleStatisticsUpdate() {
    if (systemEngine) {
        loadDatabaseStatistics();
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🌟 تحميل النظام التعليمي الشامل المحسن...');
    
    // تحديث الوقت
    updateTime();
    setInterval(updateTime, 1000); // تحديث كل ثانية
    
    // تهيئة قناة التواصل
    initializeChannel();
    
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(() => {
        if (systemEngine) {
            loadDatabaseStatistics();
        }
    }, 30000);
    
    // إضافة مستمعي الأحداث للتحسينات
    addPerformanceOptimizations();
});

// تحسينات مبسطة للأداء
function addPerformanceOptimizations() {
    // تحسين بسيط لتغيير حجم النافذة
    window.addEventListener('resize', function() {
        console.log('Window resized');
    });
}

console.log('🚀 تم تحميل ملف JavaScript الرئيسي بنجاح!');
