/*
ملف JavaScript المبسط للنظام التعليمي الشامل
بدون تموجات أو تأثيرات متحركة - أداء محسن
*/

let systemEngine = null;
let isChannelReady = false;

// إعداد قناة التواصل مع Python
function initializeChannel() {
    if (typeof qt !== 'undefined' && qt.webChannelTransport) {
        new QWebChannel(qt.webChannelTransport, function(channel) {
            systemEngine = channel.objects.systemEngine;
            isChannelReady = true;
            
            console.log('🚀 QWebChannel initialized successfully');
            
            // ربط الإشارات
            if (systemEngine) {
                systemEngine.logUpdated.connect(addLogEntry);
                systemEngine.systemStatusUpdated.connect(updateSystemStatus);
                systemEngine.moduleStatusUpdated.connect(updateModuleStatus);
                systemEngine.serverStatusUpdated.connect(updateServerStatus);
                
                // تحميل حالة النظام والإحصائيات
                loadSystemStatus();
                loadDatabaseStatistics();
                
                // فحص حالة الخادم
                systemEngine.check_server_status();
                
                console.log('✅ تم تهيئة النظام بنجاح');
            }
        });
    } else {
        console.log('⚠️ QWebChannel not available, retrying...');
        setTimeout(initializeChannel, 100);
    }
}

// تحديث الوقت
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric', 
        hour: '2-digit', 
        minute: '2-digit',
        second: '2-digit'
    });
    
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

// تحميل حالة النظام
function loadSystemStatus() {
    if (systemEngine) {
        try {
            const systemStatus = JSON.parse(systemEngine.getSystemStatus());
            console.log('System Status:', systemStatus);
        } catch (error) {
            console.error('خطأ في تحميل حالة النظام:', error);
        }
    }
}

// تحميل إحصائيات قاعدة البيانات
function loadDatabaseStatistics() {
    if (systemEngine) {
        try {
            const stats = JSON.parse(systemEngine.getDatabaseStatistics());
            
            updateStatElement('studentsCount', stats.students_count || 0);
            updateStatElement('teachersCount', stats.teachers_count || 0);
            updateStatElement('sectionsCount', stats.sections_count || 0);
            updateStatElement('secretCodesCount', stats.secret_codes_count || 0);
            
        } catch (error) {
            console.error('خطأ في تحميل الإحصائيات:', error);
        }
    }
}

// تحديث عنصر إحصائي بدون تأثيرات
function updateStatElement(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
    }
}

// تحديث حالة الخادم
function updateServerStatus(isConnected, serverInfo) {
    console.log('Server status updated:', isConnected, serverInfo);
    
    const serverStatusElements = document.querySelectorAll('.server-status');
    serverStatusElements.forEach(element => {
        if (isConnected) {
            element.textContent = 'متصل ويعمل ✅';
            element.style.color = '#28a745';
        } else {
            element.textContent = 'غير متصل ❌';
            element.style.color = '#dc3545';
        }
    });
}

// إضافة إدخال إلى السجل
function addLogEntry(message, status, timestamp) {
    console.log(`[${timestamp}] ${status.toUpperCase()}: ${message}`);
}

// تحديث حالة النظام
function updateSystemStatus(statusJson) {
    console.log('System status updated:', statusJson);
}

// تحديث حالة الوحدة
function updateModuleStatus(moduleName, status) {
    console.log(`Module ${moduleName} status: ${status}`);
}

// وظائف فتح الوحدات - مبسطة بدون تأثيرات
function openImportSystem() {
    if (systemEngine) {
        console.log('🔄 فتح نظام الاستيراد...');
        systemEngine.openImportSystem();
    } else {
        alert('❌ النظام غير جاهز بعد');
    }
}

function openSettingsSystem() {
    if (systemEngine) {
        console.log('🔄 فتح نظام الإعدادات...');
        systemEngine.openSettingsSystem();
    } else {
        alert('❌ النظام غير جاهز بعد');
    }
}

function openInstitutionSystem() {
    if (systemEngine) {
        console.log('🔄 فتح نظام بيانات المؤسسة...');
        systemEngine.openInstitutionSystem();
    } else {
        alert('❌ النظام غير جاهز بعد');
    }
}

function openStatisticsSystem() {
    if (systemEngine) {
        console.log('🔄 فتح نظام الإحصائيات...');
        systemEngine.openStatisticsSystem();
    } else {
        alert('❌ النظام غير جاهز بعد');
    }
}

function openDocumentsSystem() {
    if (systemEngine) {
        console.log('🔄 فتح نظام المستندات...');
        systemEngine.openDocumentsSystem();
    } else {
        alert('❌ النظام غير جاهز بعد');
    }
}

function openPrinterSettings() {
    if (systemEngine) {
        console.log('🔄 فتح إعدادات الطابعة...');
        systemEngine.openPrinterSettings();
    } else {
        alert('❌ النظام غير جاهز بعد');
    }
}

function manageServer() {
    if (systemEngine) {
        console.log('🔄 إدارة الخادم...');
        systemEngine.manageServer();
    } else {
        alert('❌ النظام غير جاهز بعد');
    }
}

function showHelp() {
    if (systemEngine) {
        console.log('📖 عرض المساعدة...');
        systemEngine.showSystemHelp();
    } else {
        alert('❌ النظام غير جاهز بعد');
    }
}

// تحديث مبسط للإحصائيات
function scheduleStatisticsUpdate() {
    if (systemEngine) {
        loadDatabaseStatistics();
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🌟 تحميل النظام التعليمي الشامل المبسط...');
    
    // تحديث الوقت
    updateTime();
    setInterval(updateTime, 1000); // تحديث كل ثانية
    
    // تهيئة قناة التواصل
    initializeChannel();
    
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(() => {
        if (systemEngine) {
            loadDatabaseStatistics();
        }
    }, 30000);
});

console.log('🚀 تم تحميل ملف JavaScript المبسط بنجاح!');
